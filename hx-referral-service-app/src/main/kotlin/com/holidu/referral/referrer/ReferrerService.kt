package com.holidu.referral.referrer

import com.bookiply.referral.events.ReferrerCreatedEvent
import com.holidu.logging.LogData
import com.holidu.referral.domain.legalentity.ReferrerLegalEntityId
import com.holidu.referral.infrastructure.external.marketing.ZapierService
import com.holidu.referral.infrastructure.graphql.RequestPath.CREATE_LEGAL_ENTITY
import com.holidu.referral.infrastructure.repository.payment.PaymentRepository
import com.holidu.referral.infrastructure.repository.referrer.ReferrerRepository
import com.holidu.referral.legalentity.LegalEntity
import com.holidu.referral.referrer.exceptions.*
import com.holidu.referral.utils.logger
import org.springframework.context.event.EventListener
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.stereotype.Service
import java.math.BigDecimal

@Service
class ReferrerService(
    private val legalEntityServiceTokenClient: LegalEntityClient,
    private val legalEntityUserTokenClient: LegalEntityClient,
    private val referrerRepository: ReferrerRepository,
    private val paymentRepository: PaymentRepository,
    private val zapierService: ZapierService
) {
    private val logger = logger()

    @EventListener
    fun onReferrerCodeCreated(referrerSignedUpEvent: ReferrerCreatedEvent) {
        val logData = LogData().addData("referrerSignedUpEvent", referrerSignedUpEvent)
        logData.use { logger.info("Processing referrerSignedUpEvent") }

        val existingReferrer = referrerRepository.findByLegalEntityId(referrerSignedUpEvent.referrerLegalEntityId)
        if (existingReferrer == null) {
            logData.use { logger.info("Creating referrer on referrerSignedUpEvent") }
            createReferrer(referrerSignedUpEvent)
        } else {
            // This is only a check to flag out if the referrer code has been changed
            validateReferralCodeNotChangedIfReferrerAlreadyExists(existingReferrer, referrerSignedUpEvent)
        }
    }

    fun registerMissingReferrer(referrerLegalEntityId: ReferrerLegalEntityId): Referrer {
        val logData = LogData().addData("legalEntityId", referrerLegalEntityId.value)
        val referrer = referrerRepository.findByLegalEntityId(referrerLegalEntityId.value)

        if (referrer != null) {
            logData.use { logger.info("Referrer already exists") }
            return referrer
        }

        logData.use { logger.info("Registering new referrer") }
        return saveReferrer(
            legalEntityId = referrerLegalEntityId.value,
            marketingEmailsAccepted = false,
            termsAndConditionsAccepted = false,
        ) { ReferralCode.new() }
    }

    fun registerAmbassador(
        registerAmbassadorCommand: RegisterAmbassadorCommand,
        referralCodeProvider: () -> ReferralCode = { ReferralCode.new() }
    ): Pair<LegalEntity, Referrer> {
        if (!registerAmbassadorCommand.termsAndConditionsAccepted) {
            throw TermsAndConditionsNotAcceptedException(requestPath = CREATE_LEGAL_ENTITY)
        }

        val legalEntity = legalEntityUserTokenClient.createLegalEntity(registerAmbassadorCommand)

        logger.info("Registering ambassador for legal entity: ${legalEntity.id}")
        val referrer = saveReferrer(
            legalEntity.id,
            registerAmbassadorCommand.marketingEmailsAccepted,
            registerAmbassadorCommand.termsAndConditionsAccepted,
            referralCodeProvider
        )

        createMarketingDeal(
            registerAmbassadorCommand = registerAmbassadorCommand,
            legalEntityId = legalEntity.id
        )

        return legalEntity to referrer
    }

    fun registerHost(
        legalEntity: LegalEntity,
        referralCodeProvider: () -> ReferralCode = { ReferralCode.new() }
    ): Pair<LegalEntity, Referrer> {
        val referrer = saveReferrer(
            legalEntityId = legalEntity.id,
            marketingEmailsAccepted = false,
            termsAndConditionsAccepted = false,
            referralCodeProvider = referralCodeProvider
        )

        return legalEntity to referrer
    }

    fun updateAmbassador(
        updateCommand: UpdateAmbassadorCommand
    ): Pair<LegalEntity, Referrer> {
        val referrer = referrerRepository.findByLegalEntityId(updateCommand.legalEntityId)
            ?: throw ReferrerNotFoundException("Referrer not found for legal entity ID: ${updateCommand.legalEntityId}")

        val legalEntity = legalEntityUserTokenClient.getLegalEntity(updateCommand.legalEntityId)
            ?: throw AmbassadorUpdateException("Legal entity not found for ID: ${updateCommand.legalEntityId}")

        if (!legalEntity.isAmbassador) {
            throw AmbassadorUpdateException("Legal entity ${updateCommand.legalEntityId} is not an ambassador")
        }

        val updatedLegalEntity = legalEntityUserTokenClient.updateLegalEntity(updateCommand)

        return updatedLegalEntity to referrer
    }

    fun getLegalEntity(externalUserId: String): Pair<LegalEntity?, Referrer?> {
        val legalEntityId = legalEntityUserTokenClient.findLegalEntityId() ?: return null to null
        val legalEntity = legalEntityUserTokenClient.getLegalEntity(legalEntityId) ?: return null to null
        val referrer = referrerRepository.findByLegalEntityId(legalEntity.id)

        if (!legalEntity.isAmbassador && referrer == null) {
            logger.info("Registering a new host for legal entity: ${legalEntity.id}")
            return registerHost(legalEntity)
        }

        return legalEntity to referrer
    }

    fun getLegalEntity(legalEntityId: Long): Pair<LegalEntity, Referrer?> {
        val legalEntity =
            legalEntityUserTokenClient.getLegalEntity(legalEntityId) ?: throw LegalEntityNotFoundException()
        val referrer = referrerRepository.findByLegalEntityId(legalEntity.id)

        return legalEntity to referrer
    }

    private fun saveReferrer(
        legalEntityId: Long,
        marketingEmailsAccepted: Boolean,
        termsAndConditionsAccepted: Boolean,
        referralCodeProvider: () -> ReferralCode
    ): Referrer {
        repeat(5) {
            try {
                return referrerRepository.save(
                    Referrer(
                        legalEntityId = legalEntityId,
                        referralCode = referralCodeProvider().value,
                        marketingEmailsAccepted = marketingEmailsAccepted,
                        termsAndConditionsAccepted = termsAndConditionsAccepted,
                    )
                )
            } catch (_: DataIntegrityViolationException) {
                // DataIntegrityViolationException is expected when randomly generated referral code collides with existing
            }
        }
        throw AmbassadorRegistrationException("Failed to generate a unique referral code")
    }

    private fun createMarketingDeal(
        registerAmbassadorCommand: RegisterAmbassadorCommand,
        legalEntityId: Long
    ) =
        try {
            zapierService.createMarketingDeal(registerAmbassadorCommand = registerAmbassadorCommand)
        } catch (e: Exception) {
            LogData()
                .define("legalEntityId", legalEntityId)
                .use {
                    logger.error("Failed to create marketing deal in Zapier", e)
                }
        }

    private fun createReferrer(referrerSignedUpEvent: ReferrerCreatedEvent) {
        val referrer = Referrer(
            legalEntityId = referrerSignedUpEvent.referrerLegalEntityId,
            referralCode = referrerSignedUpEvent.referralCode,
            marketingEmailsAccepted = false,
            termsAndConditionsAccepted = false
        )

        referrerRepository.save(referrer)
    }

    fun getReferrerByReferralCode(referralCode: ReferralCode): Referrer {
        return referrerRepository.findByReferralCode(referralCode.value)
            ?: throw ReferrerNotFoundException("Referrer not found for referral code: $referralCode")
    }

    fun acceptTermsAndConditions(acceptTermsAndConditionsCommand: AcceptTermsAndConditionsCommand) {
        if (acceptTermsAndConditionsCommand.termsAndConditionsAccepted.not()) {
            throw TermsAndConditionsNotAcceptedException()
        }

        val referrer = referrerRepository.findByLegalEntityId(acceptTermsAndConditionsCommand.legalEntityId)
            ?: throw ReferrerAcceptingTermsAndConditionsNotFoundException(
                message = "Referrer not found for legal entity ID: ${acceptTermsAndConditionsCommand.legalEntityId}"
            )

        legalEntityServiceTokenClient.assignReferrerRole(legalEntityId = acceptTermsAndConditionsCommand.legalEntityId)

        referrer.termsAndConditionsAccepted = acceptTermsAndConditionsCommand.termsAndConditionsAccepted
        referrer.marketingEmailsAccepted = acceptTermsAndConditionsCommand.marketingEmailsAccepted
        referrerRepository.save(referrer)
    }

    private fun validateReferralCodeNotChangedIfReferrerAlreadyExists(
        existingReferrer: Referrer,
        referrerSignedUpEvent: ReferrerCreatedEvent
    ) {
        if (existingReferrer.referralCode != referrerSignedUpEvent.referralCode) {
            LogData()
                .addData("referrerLegalEntityId", existingReferrer.legalEntityId)
                .addData("oldReferralCode", existingReferrer.referralCode)
                .addData("newReferralCode", referrerSignedUpEvent.referralCode).use {
                    logger.error("Different referral code found for the same legal entity id")
                }
            throw ReferrerValidationException("Referral code cannot be changed")
        }
    }

    fun calculateTotalRewardOf(referrerId: Long): BigDecimal {
        return paymentRepository.findByReferrerId(referrerId)
            .sumOf { it.reward }
            .let { BigDecimal.valueOf(it.toLong()) }
    }

    fun calculateTotalMonthPayment(referrerId: Long): BigDecimal {
        return paymentRepository.getTotalRewardForReferrerInCurrentMonth(referrerId)
            .let { BigDecimal.valueOf(it.toLong()) }
    }

    fun getReferrerByLegalEntityId(legalEntityId: Long): Referrer? {
        return referrerRepository.findByLegalEntityId(legalEntityId)
    }

    fun getReferrer(referrerId: Long): Referrer? {
        return referrerRepository.findByIdOrNull(referrerId)
    }
}

data class AcceptTermsAndConditionsCommand(
    val legalEntityId: Long,
    val termsAndConditionsAccepted: Boolean,
    val marketingEmailsAccepted: Boolean
)
