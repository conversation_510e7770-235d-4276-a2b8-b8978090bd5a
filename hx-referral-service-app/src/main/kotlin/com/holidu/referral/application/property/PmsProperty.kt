package com.holidu.referral.application.property

import com.holidu.logging.LogData
import com.holidu.referral.domain.property.CreatedProperty
import com.holidu.referral.domain.property.PublishedProperty
import com.holidu.referral.domain.property.ReferredProperty
import com.holidu.referral.referee.Referee
import com.holidu.referral.utils.logger
import java.time.OffsetDateTime

data class PmsProperty(
    val unitTypeId: Long,
    val parentListingId: Long,
    val refereeLegalEntityId: Long,
    val creationDate: OffsetDateTime,
    val published: Boolean,
    val propertyType: String? = null,
    val firstPublicationDate: OffsetDateTime? = null
) {

    private val logger = logger()

    fun toProperty(referee: Referee, id: Long? = null): ReferredProperty {
        return if (published) {
            if (propertyType == null || firstPublicationDate == null) {
                LogData().addData("pmsProperty", this).use {
                    logger.warn("PMS Property invalid")
                }
                throw IllegalStateException("PMS Property invalid")
            }

            PublishedProperty(
                id = id ?: 0,
                referrerId = referee.referrerId,
                refereeLegalEntityId = referee.legalEntityId,
                unitTypeId = unitTypeId,
                parentListingId = parentListingId,
                creationDate = creationDate,
                propertyType = propertyType,
                publicationDate = firstPublicationDate,
            )
        } else {
            CreatedProperty(
                id = id ?: 0,
                referrerId = referee.referrerId,
                refereeLegalEntityId = referee.legalEntityId,
                unitTypeId = unitTypeId,
                parentListingId = parentListingId,
                creationDate = creationDate
            )
        }
    }
}
