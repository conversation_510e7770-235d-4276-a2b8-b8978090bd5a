package com.holidu.referral.application.property

import com.holidu.logging.LogData
import com.holidu.referral.domain.legalentity.RefereeLegalEntityId
import com.holidu.referral.domain.property.CreatedProperty
import com.holidu.referral.domain.property.PublishedProperty
import com.holidu.referral.domain.property.ReferredProperty
import com.holidu.referral.domain.property.ReferredPropertyRepository
import com.holidu.referral.domain.property.exceptions.ReferredPropertyProcessingException
import com.holidu.referral.payment.PaymentService
import com.holidu.referral.referee.Referee
import com.holidu.referral.referee.RefereeService
import com.holidu.referral.utils.logger
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ReferredPropertyService(
    private val referredPropertyRepository: ReferredPropertyRepository,
    private val refereeService: RefereeService,
    private val pmsPropertyClient: PmsPropertyClient,
    private val paymentService: PaymentService
) {

    private val logger = logger()

    fun createProperty(unitTypeId: Long) {
        if (referredPropertyRepository.existsByUnitTypeId(unitTypeId)) {
            logger.info("Referred property already exists, ignoring the event")
            return
        }

        val pmsProperty = pmsPropertyClient.getPmsProperty(unitTypeId)
            ?: throw ReferredPropertyProcessingException("Could not get unit $unitTypeId")

        val referee = refereeService.getRefereeByLegalEntityId(pmsProperty.refereeLegalEntityId)
        if (referee == null) {
            logger.info("Property does not belong to a referee, ignoring the event")
            return
        }

        when (val property = pmsProperty.toProperty(referee)) {
            is CreatedProperty -> {
                referredPropertyRepository.saveCreatedProperty(property)
                LogData().addData("referred property id", property.id)
                    .addData("unit type id", property.unitTypeId)
                    .use { logger.info("Created referred property") }
            }

            is PublishedProperty -> throw IllegalStateException("Pms property is published")
        }
    }

    @Transactional
    fun publishProperty(unitTypeId: Long) {
        if (referredPropertyRepository.publishedPropertyExists(unitTypeId = unitTypeId)) {
            logger.info("Referred property already in the status published, ignoring the event")
            return
        }

        var createdReferredProperty = referredPropertyRepository.findCreatedReferredProperty(unitTypeId)
        val pmsProperty = pmsPropertyClient.getPmsProperty(unitTypeId)
            ?: throw ReferredPropertyProcessingException("Could not get unit $unitTypeId")

        if (createdReferredProperty == null) {
            // skip the property if not from a referred host
            val referee = refereeService.getRefereeByLegalEntityId(pmsProperty.refereeLegalEntityId)
            if (referee == null) {
                logger.info("Property does not belong to a referee, ignoring the event")
                return
            }
            createdReferredProperty = CreatedProperty(
                referrerId = referee.referrerId,
                refereeLegalEntityId = referee.legalEntityId,
                unitTypeId = pmsProperty.unitTypeId,
                parentListingId = pmsProperty.parentListingId,
                creationDate = pmsProperty.creationDate
            )
        }

        refereeService.updateRefereeName(createdReferredProperty.refereeLegalEntityId)

        if (pmsProperty.propertyType == null || pmsProperty.firstPublicationDate == null) {
            LogData().addData("pmsProperty", pmsProperty).use {
                logger.warn("PMS Property invalid")
            }
            throw IllegalStateException("PMS Property invalid")
        }

        val publishedProperty: PublishedProperty = createdReferredProperty.publish(
            propertyType = pmsProperty.propertyType,
            publicationDate = pmsProperty.firstPublicationDate
        )
        val referredProperty = referredPropertyRepository.savePublishedProperty(publishedProperty)

        LogData().addData("referred property id", referredProperty.id)
            .addData("unit type id", referredProperty.unitTypeId)
            .use { logger.info("Updated referred property") }

        paymentService.createPaymentForReferredProperty(referredProperty)

        logger.info("Created payment for referred property")
    }

    @Transactional
    fun upsertPropertiesFor(referee: Referee) {
        LogData().addData("referee", referee).use {
            logger.info("Processing after April 2025 published referee properties")
        }

        val pmsProperties = pmsPropertyClient.getAllPublishedPropertiesFor(RefereeLegalEntityId(referee.legalEntityId))
        for (pmsProperty in pmsProperties) {
            val existingReferredProperty = referredPropertyRepository.findByUnitTypeId(pmsProperty.unitTypeId)
            val property = pmsProperty.toProperty(referee, existingReferredProperty?.id)
            if (
                existingReferredProperty !is PublishedProperty &&
                property is PublishedProperty &&
                property.isPublicationAfterMarch2025
            ) {
                val publishedProperty = referredPropertyRepository.savePublishedProperty(property)
                paymentService.createPaymentForReferredProperty(publishedProperty)

                LogData().addData("referred property id", publishedProperty.id)
                    .addData("unit type id", publishedProperty.unitTypeId)
                    .use { logger.info("Published referred property") }
            }
        }
    }

    fun getReferredPropertiesForReferrer(referrerId: Long, offset: Int, limit: Int): Page<ReferredProperty> =
        referredPropertyRepository.findByReferrerId(
            referrerId,
            PageRequest.of(
                offset / limit,
                limit,
                Sort.by(ReferredProperty::creationDate.name).descending()
            )
        )
}