package com.holidu.referral.application.referee

import com.bookiply.logcontext.annotations.LogData
import com.holidu.referral.application.property.ReferredPropertyService
import com.holidu.referral.domain.legalentity.RefereeLegalEntityId
import com.holidu.referral.domain.legalentity.ReferrerLegalEntityId
import com.holidu.referral.referee.Referee
import com.holidu.referral.referee.RefereeService
import com.holidu.referral.referrer.ReferrerService
import com.holidu.referral.utils.logger
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class ManualRefereeCreationService(
    private val referrerService: ReferrerService,
    private val refereeService: RefereeService,
    private val referredPropertyService: ReferredPropertyService,
) {
    private val logger = logger()

    @Transactional
    fun createReferee(
        @LogData refereeLegalEntityId: RefereeLegalEntityId,
        @LogData referrerLegalEntityId: ReferrerLegalEntityId
    ): Referee {
        logger.info("Creating referee and referred properties")

        val referrer = referrerService.registerMissingReferrer(referrerLegalEntityId)
        val referee = refereeService.createMissingReferee(
            referrer = referrer,
            refereeLegalEntityId = refereeLegalEntityId
        )

        referredPropertyService.upsertPropertiesFor(referee)

        logger.info("Created referee and referred properties")
        return referee
    }
}
