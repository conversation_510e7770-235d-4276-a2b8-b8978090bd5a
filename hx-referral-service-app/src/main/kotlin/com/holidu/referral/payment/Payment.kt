package com.holidu.referral.payment

import com.bookiply.datalake.records.AgentReferralPayoutRecord
import com.holidu.referral.infrastructure.datalake.DatalakeRecord
import com.holidu.referral.infrastructure.datalake.Indexable
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.SQLRestriction
import org.hibernate.annotations.UpdateTimestamp
import java.time.Instant
import java.time.OffsetDateTime

typealias PaymentDatalakeRecord = AgentReferralPayoutRecord

@Entity
@SQLRestriction("deleted_at IS NULL")
data class Payment(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,
    val referrerId: Long,
    var referrerLegalEntityId: Long,
    var refereeLegalEntityId: Long? = null,
    val creationDate: OffsetDateTime,
    val reward: Int,
    val bonusId: Long? = null,
    val unitTypeId: Long? = null,
    @Enumerated(EnumType.STRING)
    val linkedEntityType: LinkedEntityType,
    @Enumerated(EnumType.STRING)
    var state: PaymentState,
    @CreationTimestamp
    val createdAt: Instant = Instant.now(),
    @UpdateTimestamp
    val updatedAt: Instant = Instant.now(),
    private val deletedAt: Instant? = null
) : Indexable {
    init {
        check(bonusId != null || unitTypeId != null)
    }

    fun isReward(): Boolean =
        linkedEntityType == LinkedEntityType.REFERRED_PROPERTY

    fun isBonus(): Boolean =
        linkedEntityType == LinkedEntityType.BONUS

    fun isPending(): Boolean =
        state == PaymentState.PENDING

    override val firehoseStreamName: String
        // Before April 2025, the referral logic lived in bookiply-backend.
        // We reuse the stream which bookiply-backend was sending the data to,
        // in order to leverage the existing processes in Datalake and
        // not set up a new stream and BI pipeline
        get() = "firehose_bookiply_data_agent_referral_payout"

    override fun toDatalakeRecord(): DatalakeRecord {
        val payment = this

        return PaymentDatalakeRecord.newBuilder().apply {
            this.apartmentId = payment.unitTypeId
            this.referrerAgentId = payment.referrerLegalEntityId
            this.refereeAgentId = payment.refereeLegalEntityId
            this.unitTypeId = payment.unitTypeId
            this.createdAt = payment.creationDate.toInstant()
            this.referralAmount = payment.reward.toBigDecimal()
            this.type = payment.linkedEntityType.toPaymentType().name
        }.build()
    }
}

