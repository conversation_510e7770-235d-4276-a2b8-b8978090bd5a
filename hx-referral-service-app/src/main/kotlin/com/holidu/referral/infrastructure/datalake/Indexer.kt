package com.holidu.referral.infrastructure.datalake

import com.bookiply.infrastructure.events.publishers.CommandPublisher
import com.fasterxml.jackson.databind.ObjectMapper
import com.holidu.logging.LogData
import com.holidu.referral.infrastructure.events.datalake.IndexBatchDatalakeRecordsCommand
import com.holidu.referral.infrastructure.events.datalake.IndexSingleDatalakeRecordCommand
import com.holidu.referral.utils.logger
import org.springframework.stereotype.Service

@Service
class Indexer(
    private val commandPublisher: CommandPublisher
) {
    private val logger = logger()

    fun index(entity: Indexable) =
        try {
            LogData()
                .define("indexedClass", entity::class::simpleName)
                .use {
                    logger.debug("Sending command to index single entity")
                }

//            try {
//                ObjectMapper().writeValueAsString(entity.toDatalakeRecord())
//            } catch (e: Exception) {
//
//
//                LogData()
//                    .define("indexedClass", entity::class::simpleName)
//                    .use {
//                        logger.error("Failed to serialize entity", e)
//                    }
//            }

            commandPublisher.sendMessage(
                command = IndexSingleDatalakeRecordCommand(
                    record = entity.toDatalakeRecord(),
                    streamName = entity.firehoseStreamName
                )
            )
        } catch (e: Exception) {
            LogData()
                .define("indexedClass", entity::class::simpleName)
                .use {
                    logger.error("Failed to send command to index single entity", e)
                }
        }

    fun indexBatch(entities: Collection<Indexable>) =
        try {
            commandPublisher.sendMessage(
                command = IndexBatchDatalakeRecordsCommand(
                    records = entities.map { it.toDatalakeRecord() },
                    streamName = entities.first().firehoseStreamName
                )
            )
        } catch (e: Exception) {
            LogData()
                .define("indexedClass", entities.first()::class::simpleName)
                .define("entitiesCount", entities.size)
                .use {
                    logger.error("Failed to send command to index batch of entities", e)
                }
        }
}