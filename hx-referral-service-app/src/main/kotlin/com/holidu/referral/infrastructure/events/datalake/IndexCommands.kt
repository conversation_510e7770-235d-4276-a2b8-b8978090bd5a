package com.holidu.referral.infrastructure.events.datalake

import com.holidu.referral.activemq.InternalReferralServiceCommand
import com.holidu.referral.infrastructure.datalake.DatalakeRecord

sealed interface IndexCommand {
    val streamName: String
}

data class IndexSingleDatalakeRecordCommand(
    // fixme this
    val record: DatalakeRecord,
    override val streamName: String
) : InternalReferralServiceCommand(), IndexCommand

data class IndexBatchDatalakeRecordsCommand(
    val records: Collection<DatalakeRecord>,
    override val streamName: String
) : InternalReferralServiceCommand(), IndexCommand