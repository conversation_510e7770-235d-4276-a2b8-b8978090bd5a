package com.holidu.referral.infrastructure.datalake

import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.Payment
import com.holidu.referral.payment.PaymentState
import com.holidu.referral.referee.Referee
import com.holidu.referral.referrer.Referrer
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC

// fixme internal endpoint should start with /internal
@RestController
@RequestMapping("/rest/api/v1/datalake/indexing")
class TestIndexingController(
    private val indexer: Indexer
) {
    @PostMapping("/test")
    fun test() {
        val referrer = Referrer(
            id = 1,
            legalEntityId = 123,
            referralCode = "ABC12345",
            createdAt = MIDNIGHT_JANUARY_1ST_2000.toInstant(),
            updatedAt = MIDNIGHT_JANUARY_1ST_2000.toInstant(),
            termsAndConditionsAccepted = true,
            marketingEmailsAccepted = false
        )

        val referee = Referee(
            id = 1,
            legalEntityId = 567,
            referrerId = 1,
            referrerLegalEntityId = 123,
            createdAt = MIDNIGHT_JANUARY_1ST_2000.plusDays(1).toInstant(),
            updatedAt = MIDNIGHT_JANUARY_1ST_2000.plusDays(1).toInstant()
        )

        val payment = Payment(
            id = 1,
            referrerId = 1,
            referrerLegalEntityId = 123,
            refereeLegalEntityId = 123,
            unitTypeId = 345,
            creationDate = MIDNIGHT_JANUARY_1ST_2000.plusMonths(1),
            reward = 75,
            linkedEntityType = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.PENDING
        )

        indexer.index(referrer)
//        indexer.index(referee)
//        indexer.index(payment)
    }

    companion object {
        private val MIDNIGHT_JANUARY_1ST_2000 = OffsetDateTime.of(
            2000,
            1,
            1,
            0,
            0,
            0,
            0,
            UTC
        )
    }
}