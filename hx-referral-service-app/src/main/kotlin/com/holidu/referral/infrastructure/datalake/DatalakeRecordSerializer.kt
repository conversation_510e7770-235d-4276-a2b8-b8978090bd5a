package com.holidu.referral.infrastructure.datalake

import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.SerializerProvider
import com.holidu.firehose.utiltity.AvroSerializer

class DatalakeRecordSerializer : JsonSerializer<DatalakeRecord>() {
    // fixme explain why
    override fun serialize(
        value: DatalakeRecord,
        gen: JsonGenerator,
        serializers: SerializerProvider?
    ) {
        gen.writeRaw(
            AvroSerializer.serializeDTO(
                data = value,
                streamName = PLACEHOLDER_STREAM_NAME
            ).data
        )
    }

    companion object {
        private const val PLACEHOLDER_STREAM_NAME = "NOT_USED"
    }
}