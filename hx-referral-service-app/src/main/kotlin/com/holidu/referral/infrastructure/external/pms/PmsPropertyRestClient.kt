package com.holidu.referral.infrastructure.external.pms

import com.bookiply.apartment.dto.UnitTypeDto
import com.bookiply.apartment.dto.UnitTypeDto.PARENT_LISTING_ID_FIELD
import com.bookiply.apartment.dto.UnitTypeDto.PUBLISHED_FIELD
import com.bookiply.apartment.dto.UnitTypeSearchRequest
import com.bookiply.collaborator.dto.AgentParentListingRelationDto
import com.bookiply.collaborator.dto.AgentParentListingRelationSearchRequest
import com.bookiply.collaborator.model.PmcAgentParentListingRelationType
import com.bookiply.utils.services.search.model.comparisonoperations.BooleanComparisonOperation
import com.bookiply.utils.services.search.model.comparisonoperations.EnumComparisonOperation
import com.bookiply.utils.services.search.model.comparisonoperations.NumberComparisonOperation
import com.holidu.referral.application.property.PmsProperty
import com.holidu.referral.application.property.PmsPropertyClient
import com.holidu.referral.domain.legalentity.RefereeLegalEntityId
import com.holidu.referral.pms.client.ParentListingGeneralInfoClient
import com.holidu.referral.pms.client.ParentListingGeneralInfoResponseDto
import com.holidu.referral.pms.client.UnitTypeAgentClient
import com.holidu.referral.pms.client.UnitTypeClient
import com.holidu.referral.pms.exception.PmsClientException
import com.holidu.referral.utils.logger
import org.springframework.stereotype.Component
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId

@Component
class PmsPropertyRestClient(
    private val unitTypeClient: UnitTypeClient,
    private val unitTypeAgentClient: UnitTypeAgentClient,
    private val parentListingGeneralInfoClient: ParentListingGeneralInfoClient
) : PmsPropertyClient {

    private val logger = logger()

    override fun getPmsProperty(unitTypeId: Long): PmsProperty? {
        return try {
            val unitType = unitTypeClient.getUnitType(unitTypeId)
            if (unitType.isDeleted) {
                return null
            }

            val refereeLegalEntityId = getLegalEntityIdForParentListing(unitType.parentListingId)
                ?: throw PmsClientException("Referee legal entity id not found for unit ${unitType.id}")
            val firstPublicationDate = if (unitType.isPublished) {
                getFirstPublishDate(unitType.parentListingId, unitType.id)
            } else null

            toPmsProperty(unitType, refereeLegalEntityId, firstPublicationDate)
        } catch (e: Exception) {
            logger.error("Failed to get pms property", e)
            null
        }
    }

    override fun getAllPublishedPropertiesFor(refereeLegalEntityId: RefereeLegalEntityId): List<PmsProperty> {
        return try {
            val parentListingRelations = unitTypeAgentClient.findAgentParentListingRelation(
                AgentParentListingRelationSearchRequest().init(
                    AgentParentListingRelationDto.AGENT_ID_FIELD,
                    NumberComparisonOperation.EQ,
                    refereeLegalEntityId.value
                ).and(
                    AgentParentListingRelationDto.RELATION_TYPE_FIELD,
                    EnumComparisonOperation.EQ,
                    PmcAgentParentListingRelationType.EDITOR
                )
            ).results

            if (parentListingRelations.isEmpty()) {
                return emptyList()
            }

            val unitTypeSearchRequest = parentListingRelations.fold(UnitTypeSearchRequest()) { acc, relation ->
                acc.or(
                    PARENT_LISTING_ID_FIELD,
                    NumberComparisonOperation.EQ,
                    relation.parentListingId
                ).and(
                    PUBLISHED_FIELD,
                    BooleanComparisonOperation.EQ,
                    true
                )
            }
            val unitTypeDtos = unitTypeClient.findUnitTypes(unitTypeSearchRequest).results

            unitTypeDtos.map {
                toPmsProperty(
                    unitType = it,
                    refereeLegalEntityId = refereeLegalEntityId.value,
                    firstPublicationDate = getFirstPublishDate(it.parentListingId, it.id)
                )
            }
        } catch (e: Exception) {
            logger.error("Failed to get all pms properties", e)
            throw e
        }
    }

    fun getLegalEntityIdForParentListing(parentListingId: Long): Long? {
        val searchRequest = AgentParentListingRelationSearchRequest().init(
            AgentParentListingRelationDto.PARENT_LISTING_ID_FIELD,
            NumberComparisonOperation.EQ,
            parentListingId
        ).and(
            AgentParentListingRelationDto.RELATION_TYPE_FIELD,
            EnumComparisonOperation.EQ,
            PmcAgentParentListingRelationType.EDITOR
        )
        val relations = unitTypeAgentClient.findAgentParentListingRelation(searchRequest)
        return relations.results.first()?.agentId
    }

    fun getFirstPublishDate(parentListingId: Long, unitTypeId: Long): OffsetDateTime? {
        val unitTypeGeneralInfo = getParentListingGeneralInfo(parentListingId)
            .unitTypes
            .first { it.id == unitTypeId }

        return convertToCET(unitTypeGeneralInfo.unitTypePublishInfoDto?.firstPublished)
    }

    private fun toPmsProperty(
        unitType: UnitTypeDto,
        refereeLegalEntityId: Long,
        firstPublicationDate: OffsetDateTime?
    ) = PmsProperty(
        unitTypeId = unitType.id,
        parentListingId = unitType.parentListingId,
        refereeLegalEntityId = refereeLegalEntityId,
        creationDate = unitType.createdAt,
        published = unitType.isPublished,
        propertyType = unitType.pmcApartmentType?.stringValue,
        firstPublicationDate = firstPublicationDate,
    )

    private fun getParentListingGeneralInfo(parentListingId: Long): ParentListingGeneralInfoResponseDto = try {
        parentListingGeneralInfoClient.getGeneralInfo(parentListingId)
    } catch (e: Exception) {
        throw PmsClientException("Could not get parent listing general info with id parentListingId", e)
    }

    private fun convertToCET(firstPublished: LocalDateTime?): OffsetDateTime? {
        if (firstPublished == null) {
            return null
        }

        val offset = ZoneId.of("CET").rules.getOffset(firstPublished)
        return firstPublished.atOffset(offset)
    }
}