package com.holidu.referral.infrastructure.api.referee

import com.holidu.referral.api.referee.RefereeApi
import com.holidu.referral.api.referee.RefereeDto
import com.holidu.referral.application.referee.ManualRefereeCreationService
import com.holidu.referral.domain.legalentity.RefereeLegalEntityId
import com.holidu.referral.domain.legalentity.ReferrerLegalEntityId
import com.holidu.referral.utils.logger
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.server.ResponseStatusException
import java.time.ZoneOffset

@RestController
class RefereeController(
    private val manualRefereeCreationService: ManualRefereeCreationService
) : RefereeApi {
    private val logger = logger()

    override fun createReferee(@RequestBody refereeDto: RefereeDto): RefereeDto {
        val createdReferee = try {
            manualRefereeCreationService.createReferee(
                refereeLegalEntityId = RefereeLegalEntityId(refereeDto.legalEntityId),
                referrerLegalEntityId = ReferrerLegalEntityId(refereeDto.referrerLegalEntityId)
            )
        } catch (e: IllegalArgumentException) {
            logger.error("Failed to create referee", e)
            throw ResponseStatusException(HttpStatus.BAD_REQUEST)
        } catch (e: Exception) {
            logger.error("Failed to create referee", e)
            throw ResponseStatusException(HttpStatus.INTERNAL_SERVER_ERROR)
        }

        return RefereeDto(
            legalEntityId = createdReferee.legalEntityId,
            referrerLegalEntityId = refereeDto.referrerLegalEntityId,
            creationDate = createdReferee.createdAt.atOffset(ZoneOffset.UTC)
        )
    }
}
