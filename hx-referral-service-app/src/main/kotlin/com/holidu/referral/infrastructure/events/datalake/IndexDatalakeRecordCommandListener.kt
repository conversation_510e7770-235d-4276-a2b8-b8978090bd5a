package com.holidu.referral.infrastructure.events.datalake

import com.bookiply.infrastructure.events.CommandListener
import com.holidu.logging.LogData
import com.holidu.referral.infrastructure.datalake.DatalakeIndexer
import com.holidu.referral.utils.logger
import org.springframework.stereotype.Component

@Component
@Suppress("unused")
class IndexDatalakeRecordCommandListener(
    private val datalakeIndexer: DatalakeIndexer
) {
    private val logger = logger()

    @CommandListener
    @Suppress("unused")
    fun onIndexSingleDatalakeRecordCommand(indexSingleDatalakeRecordCommand: IndexSingleDatalakeRecordCommand) {
        LogData()
            .define("record", indexSingleDatalakeRecordCommand.record)
            .use {
                logger.debug("Processing single record indexing")
            }

        datalakeIndexer.index(
            datalakeRecord = indexSingleDatalakeRecordCommand.record,
            streamName = indexSingleDatalakeRecordCommand.streamName
        )
    }

    @CommandListener
    @Suppress("unused")
    fun onIndexBatchDatalakeRecordsCommand(indexBatchDatalakeRecordsCommand: IndexBatchDatalakeRecordsCommand) {
        LogData()
            .define("firstRecords", indexBatchDatalakeRecordsCommand.records.first())
            .define("recordsCount", indexBatchDatalakeRecordsCommand.records.size)
            .use {
                logger.debug("Processing batch records indexing")
            }

        datalakeIndexer.indexBatch(
            datalakeRecords = indexBatchDatalakeRecordsCommand.records,
            streamName = indexBatchDatalakeRecordsCommand.streamName
        )
    }
}