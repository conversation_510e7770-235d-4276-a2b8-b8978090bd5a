package com.holidu.referral.infrastructure.datalake

import com.holidu.referral.config.security.AdminPermission
import com.holidu.referral.infrastructure.repository.payment.PaymentRepository
import com.holidu.referral.infrastructure.repository.referee.RefereeRepository
import com.holidu.referral.infrastructure.repository.referrer.ReferrerRepository
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@AdminPermission
@RequestMapping("/internal/api/v1/datalake/indexing")
class DatalakeIndexingController(
    private val referrerRepository: ReferrerRepository,
    private val refereeRepository: RefereeRepository,
    private val paymentRepository: PaymentRepository
) {
    @PostMapping("/index-all")
    fun indexAll(indexAllCommand: IndexAllCommand): IndexAllResponse {
        val entityGetter: ObjectsGetter = when(indexAllCommand.typeToIndex) {
            IndexedType.REFERRER -> ObjectsGetter { referrerRepository.findAll() }
            IndexedType.REFEREE -> ObjectsGetter { refereeRepository.findAll() }
            IndexedType.PAYMENT -> ObjectsGetter { paymentRepository.findAll() }
        }

        /*
        In Repositories, there should be saveAndIndex
        lik

        .also {

                // fixme the message should contain the SpecificRecordBase and stream named
                // this should be created by a separate service
                commandPublisher.sendMessage(
                    command = IndexEntityCommand(entity)
                )
            }
         */

        /*
        repository.findAll().forEach {
            indexer.index(it)
        }
         */

        val all = entityGetter.getAll()
        // fixme index

        return IndexAllResponse(count = all.size)
    }

}

data class IndexAllCommand(
    val typeToIndex: IndexedType
)

data class IndexAllResponse(
    val count: Int
)

enum class IndexedType {
    REFERRER,
    REFEREE,
    PAYMENT
}

fun interface ObjectsGetter {
    fun getAll(): List<Any>
}