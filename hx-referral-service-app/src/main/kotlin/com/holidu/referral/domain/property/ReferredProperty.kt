package com.holidu.referral.domain.property

import com.bookiply.apartment.model.ApartmentTypeEnum
import java.time.OffsetDateTime

sealed interface ReferredProperty {
    val id: Long
    val referrerId: Long
    val refereeLegalEntityId: Long
    val unitTypeId: Long
    val parentListingId: Long
    val creationDate: OffsetDateTime
}

data class CreatedProperty(
    override val id: Long = 0,
    override val referrerId: Long,
    override val refereeLegalEntityId: Long,
    override val unitTypeId: Long,
    override val parentListingId: Long,
    override val creationDate: OffsetDateTime,
) : ReferredProperty {
    fun publish(propertyType: String, publicationDate: OffsetDateTime): PublishedProperty = PublishedProperty(
        id = id,
        referrerId = referrerId,
        refereeLegalEntityId = refereeLegalEntityId,
        unitTypeId = unitTypeId,
        parentListingId = parentListingId,
        creationDate = creationDate,
        propertyType = propertyType,
        publicationDate = publicationDate
    )
}

data class PublishedProperty(
    override val id: Long = 0,
    override val referrerId: Long,
    override val refereeLegalEntityId: Long,
    override val unitTypeId: Long,
    override val parentListingId: Long,
    override val creationDate: OffsetDateTime,
    var propertyType: String,
    var publicationDate: OffsetDateTime,
) : ReferredProperty {

    val isPublicationAfterMarch2025: Boolean = !isPublicationBeforeApril2025

    val isPublicationBeforeApril2025: Boolean
        get() = publicationDate.isBefore(OffsetDateTime.parse("2025-04-01T00:00:00+02:00"))

    fun isRoom(): Boolean {
        return ApartmentTypeEnum.ROOM.name == this.propertyType
    }
}
