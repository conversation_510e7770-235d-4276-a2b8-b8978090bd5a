package com.holidu.referral.referee

import com.bookiply.datalake.records.AgentReferralRecord
import com.holidu.referral.infrastructure.datalake.DatalakeRecord
import com.holidu.referral.infrastructure.datalake.Indexable
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.time.Instant

typealias RefereeDatalakeRecord = AgentReferralRecord

@Entity
data class Referee (
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0,

    @Column(nullable = false,  unique = true)
    var legalEntityId: Long,

    @Column(nullable = false)
    var referrerId: Long,

    @Column(nullable = false)
    var referrerLegalEntityId: Long,

    /**
     * This is a masked name of the referee, It is used for display purposes only.
     */
    var name: String? = null,

    @CreationTimestamp
    val createdAt: Instant = Instant.now(),

    @UpdateTimestamp
    val updatedAt: Instant = Instant.now(),
) : Indexable {
    override val firehoseStreamName: String
        // Before April 2025, the referral logic lived in bookiply-backend.
        // We reuse the stream which bookiply-backend was sending the data to,
        // in order to leverage the existing processes in Datalake and
        // not set up a new stream and BI pipeline
        get() = "firehose_bookiply_data_agent_referral"

    override fun toDatalakeRecord(): DatalakeRecord {
        val referee = this

        return RefereeDatalakeRecord.newBuilder().apply {
            this.agentId = referee.legalEntityId
            this.referrerAgentId = referee.referrerLegalEntityId
            this.referralCode = null
            this.createdAt = referee.createdAt
            this.deletedAt = null
        }.build()
    }
}