package com.holidu.referral.referee

import com.bookiply.referral.events.RefereeSignedUpEvent
import com.holidu.logging.LogData
import com.holidu.referral.domain.legalentity.RefereeLegalEntityId
import com.holidu.referral.infrastructure.repository.referee.RefereeRepository
import com.holidu.referral.legalentity.LegalEntity
import com.holidu.referral.referee.exception.RefereeCreationException
import com.holidu.referral.referrer.LegalEntityClient
import com.holidu.referral.referrer.ReferralCode
import com.holidu.referral.referrer.Referrer
import com.holidu.referral.referrer.ReferrerService
import com.holidu.referral.utils.logger
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service

@Service
class RefereeService(
    private val referrerService: ReferrerService,
    private val refereeRepository: RefereeRepository,
    private val legalEntityServiceTokenClient: LegalEntityClient,
) {
    private val log = logger()

    @EventListener
    fun onRefereeSignUp(refereeSignedUpEvent: RefereeSignedUpEvent) {
        val referrer = referrerService.getReferrerByReferralCode(
            ReferralCode.of(refereeSignedUpEvent.referralCode)
        )
        val refereeLegalEntityId = refereeSignedUpEvent.refereeLegalEntityId
        val existingReferee = refereeRepository.findByLegalEntityId(refereeLegalEntityId)

        if (existingReferee == null) {
            createReferee(refereeLegalEntityId, referrer)
        } else {
            // this could happen as AMs can manually add / correct referrers for referees in the backoffice
            updateRefereeIfReferrerChanged(existingReferee, referrer)
        }
    }

    fun getRefereeByLegalEntityId(legalEntityId: Long): Referee? =
        refereeRepository.findByLegalEntityId(legalEntityId)

    fun updateRefereeName(refereeLegalEntityId: Long) {
        val referee = getRefereeByLegalEntityId(refereeLegalEntityId)
            ?: throw RefereeCreationException("Referee not found for Legal Entity ID: $refereeLegalEntityId")

        referee.name = getLegalEntity(refereeLegalEntityId).getMaskedName()
        refereeRepository.save(referee)
    }

    fun createMissingReferee(
        referrer: Referrer,
        refereeLegalEntityId: RefereeLegalEntityId,
    ): Referee {
        val referee = refereeRepository.findByLegalEntityId(refereeLegalEntityId.value)
        if (referee != null) {
            LogData()
                .addData("refereeLegalEntityId", refereeLegalEntityId)
                .use {
                    log.info("Referee already exists")
                }

            if (referee.referrerId != referrer.id) {
                throw IllegalArgumentException("Existing referee belongs to a different referrer ${referee.referrerId}")
            }

            return referee
        }

        return createReferee(refereeLegalEntityId.value, referrer)
    }

    private fun createReferee(
        refereeLegalEntityId: Long,
        referrer: Referrer
    ): Referee {
        val referee = Referee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            name = getLegalEntity(refereeLegalEntityId).getMaskedName()
        )
        return refereeRepository.save(referee)
    }

    private fun updateRefereeIfReferrerChanged(
        existingReferee: Referee,
        referrer: Referrer
    ) {
        if (existingReferee.referrerId != referrer.id) {
            LogData()
                .addData("refereeId", existingReferee.id)
                .addData("oldReferrer", existingReferee.referrerId)
                .addData("newReferrer", referrer)
                .use {
                    log.info("Updating referee as referrer has changed")
                }

            existingReferee.referrerId = referrer.id
            existingReferee.referrerLegalEntityId = referrer.legalEntityId

            refereeRepository.save(existingReferee)
        }
    }

    private fun getLegalEntity(refereeLegalEntityId: Long): LegalEntity {
        return legalEntityServiceTokenClient.getLegalEntity(refereeLegalEntityId)
            ?: throw RefereeCreationException("Legal Entity not found for ID: $refereeLegalEntityId")
    }

    fun getRefereesByReferrerId(referrerId: Long): List<Referee> {
        return refereeRepository.findByReferrerId(referrerId)
    }
}