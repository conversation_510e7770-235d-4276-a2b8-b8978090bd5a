package com.holidu.referral.config.security

import org.springframework.boot.actuate.autoconfigure.security.servlet.EndpointRequest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Profile
import org.springframework.http.HttpMethod
import org.springframework.security.config.annotation.web.AuthorizeHttpRequestsDsl
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.invoke
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.web.SecurityFilterChain


private const val HX_REFERRAL_SERVICE_ADMIN = "HX_REFERRAL_SERVICE_ADMIN"
private const val PMS_ACCOUNT_MANAGEMENT = "ACCOUNT_MANAGEMENT"
private const val PMS_SALES_MANAGEMENT = "SALES_MANAGEMENT"
private const val PMS_AREA_LEADS = "AREA_LEADS"

@Configuration
@EnableWebSecurity
class ServiceSecurityConfiguration {

    @Bean
    @Profile("!test & !disable-security")
    fun keycloakSecurityConfigure(http: HttpSecurity): SecurityFilterChain {
        http {
            cors { }
            csrf {
                disable()
            }
            sessionManagement {
                sessionCreationPolicy = SessionCreationPolicy.STATELESS
            }
            authorizeHttpRequests {
                // Remove authorization on actuator endpoints
                authorize(EndpointRequest.toAnyEndpoint(), permitAll)

                authorize("/graphiql/**", permitAll)

                authorizeRestApi()
                authorize("/**", authenticated)
            }
            oauth2ResourceServer {
                jwt { }
            }
        }

        return http.build()
    }

    @Bean
    @Profile("test | disable-security")
    fun noGraphQlSecurity(http: HttpSecurity): SecurityFilterChain {
        http {
            csrf {
                disable()
            }
            authorizeHttpRequests {
                authorizeRestApi()
                authorize(anyRequest, permitAll)
            }
        }

        return http.build()
    }

    private fun AuthorizeHttpRequestsDsl.authorizeRestApi() {
        authorize("/internal/**", hasAuthority(HX_REFERRAL_SERVICE_ADMIN))
        authorize(
            HttpMethod.POST, "/v1/referees", hasAnyAuthority(
                PMS_ACCOUNT_MANAGEMENT, PMS_SALES_MANAGEMENT, PMS_AREA_LEADS
            )
        )
    }
}
