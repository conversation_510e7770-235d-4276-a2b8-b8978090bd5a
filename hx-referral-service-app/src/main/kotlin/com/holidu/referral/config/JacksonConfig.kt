package com.holidu.referral.config

import com.fasterxml.jackson.databind.module.SimpleModule
import com.holidu.referral.infrastructure.datalake.DatalakeRecord
import com.holidu.referral.infrastructure.datalake.DatalakeRecordSerializer
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class JacksonConfig {
    @Bean
    fun customJacksonModule(): SimpleModule {
        return SimpleModule().apply {
            addSerializer(DatalakeRecord::class.java, DatalakeRecordSerializer())
            addDeserializer(DatalakeRecord::class.java, DatalakeRecordSerializer())
        }
    }
}