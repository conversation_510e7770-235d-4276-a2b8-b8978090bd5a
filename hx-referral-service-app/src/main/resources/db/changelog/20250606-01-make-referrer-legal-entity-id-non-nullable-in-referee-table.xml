<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="20250606-01-make-referrer-legal-entity-id-non-nullable-in-referee-table" author="<PERSON><PERSON>ramaren<PERSON>">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="referee"/>
        </preConditions>

        <addNotNullConstraint tableName="referee" columnName="referrer_legal_entity_id"/>
    </changeSet>
</databaseChangeLog>