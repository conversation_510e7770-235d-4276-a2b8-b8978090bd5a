<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                      http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.5.xsd">

    <changeSet id="20250605-01-make-unit-type-id-unique-in-referred-property" author="<PERSON><PERSON><PERSON><PERSON>">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="referred_property"/>
            <columnExists tableName="referred_property" columnName="unit_type_id"/>
        </preConditions>

        <addUniqueConstraint tableName="referred_property" columnNames="unit_type_id"/>
    </changeSet>

</databaseChangeLog>