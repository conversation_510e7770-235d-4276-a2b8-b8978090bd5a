<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                   https://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="20250605-01-add_constraints_payment_table" author="Ketan Patil">
        <preConditions onFail="MARK_RAN">
            <tableExists tableName="payment"/>
        </preConditions>

        <sql>

            -- only one of bonus_id or unit_type_id can be not null
            ALTER TABLE payment
                ADD CONSTRAINT either_bonus_id_or_unit_type_id_is_not_null
                    CHECK (
                        (bonus_id IS NULL AND unit_type_id IS NOT NULL) OR
                        (bonus_id IS NOT NULL AND unit_type_id IS NULL)
                        );

            -- add unique constraint for bonus_id or unit_type_id
            ALTER TABLE payment
                ADD UNIQUE NULLS NOT DISTINCT (bonus_id, unit_type_id, deleted_at);

        </sql>

    </changeSet>
</databaseChangeLog>