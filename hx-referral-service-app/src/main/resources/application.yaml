spring:
  main:
    banner-mode: off
  application:
    name: referral
  threads:
    virtual:
      enabled: true
  graphql:
    graphiql:
      enabled: true
    cors:
      allowed-origin-patterns: http://localhost:*, https://*.holidu.com, https://*.holidu.io

  security:
    oauth2:
      resourceserver:
        jwt:
          jws-algorithms: RS256
          # second audience is the client id of keycloak referral service client, it is used for token exchange
          audiences: hx-referral-platform,hx-referral-platform-backend-client
          authorities-claim-name: roles
          authority-prefix: ""
      client:
        registration:
          referral-platform-backend-client:
            provider: keycloak-server
            authorization-grant-type: client_credentials
            client-id: ${aws.secret.app.oauth2.backend.client.id}
            client-secret: ${aws.secret.app.oauth2.backend.client.secret}
            scope:
              - hx-backoffice-scope
              - hx-pms-scope
          legal-entities-client:
            provider: keycloak-server
            authorization-grant-type: client_credentials
            client-id: ${aws.secret.app.oauth2.backend.client.id}
            client-secret: ${aws.secret.app.oauth2.backend.client.secret}
            scope:
#              - hx-legal-entities-scope
              - hx-backoffice-scope
          legal-entities-client-with-token-exchange:
            provider: keycloak-server
            authorization-grant-type: urn:ietf:params:oauth:grant-type:token-exchange
            client-id: ${aws.secret.app.oauth2.backend.client.id}
            client-secret: ${aws.secret.app.oauth2.backend.client.secret}
            client-authentication-method: client_secret_post
            scope:
#              The double token exchange (referral -> legal entities -> backoffice) does not work in current keycloak version.
#              Temporarily calling backoffice directly.
#              - hx-legal-entities-backend-client
              - hx-backoffice-backend-client

  autoconfigure:
    exclude: org.springframework.boot.autoconfigure.jms.activemq.ActiveMQAutoConfiguration

  activemq:
    brokers:
      BookiplyBroker:
        user: "${BookiplyActivemq.user}"
        password: "${BookiplyActivemq.password}"
        broker-url: "${BookiplyActivemq.broker-url}"
      ReferralServiceBroker:
        user: "${ReferralServiceActivemq.user}"
        password: "${ReferralServiceActivemq.password}"
        broker-url: "${ReferralServiceActivemq.broker-url}"

  data:
    redis:
      port: 6379

  datasource:
    username: "${db.username}"
    password: "${db.password}"
    hikari:
      data-source-properties:
        statement_timeout: 10000 #ms
        idle_in_transaction_session_timeout: 20000  #ms
        transaction_timeout: 30000 #ms
      leak-detection-threshold: 5000
      maximum-pool-size: 10
      auto-commit: false

  jpa:
    open-in-view: false
    hibernate:
      ddl-auto: none
    properties:
      org.hibernate.envers:
        store_data_at_delete: true
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        jdbc:
          time_zone: UTC
        connection:
          provider_disables_autocommit: true
        default_batch_fetch_size: 30

  liquibase:
    enabled: true
    change-log: classpath:db/liquibase-changelog.xml

elastic:
  apm:
    active: false
    server_urls: http://apm-server-apm-server.monitoring:8200
    application_packages: com.holidu.referral
    transaction_sample_rate: 0.1
    transaction_ignore_urls: "/actuator*"
    service_name: hx-referral-service
    log_level: INFO
    log_format_sout: JSON

management:
  endpoint:
    health:
      probes:
        enabled: true
      show-components: always
  endpoints:
    web:
      exposure:
        include: health,prometheus
  server:
    port: 8081

logging:
  config: classpath:logback.xml
  level:
    root: info
    io.awspring.cloud: debug
    org.springframework.boot.diagnostics: debug

bookiply:
  liquibase:
    advisory-locks: true

sentry:
  enabled: false
  logging:
    minimum-event-level: error
    minimum-breadcrumb-level: info
