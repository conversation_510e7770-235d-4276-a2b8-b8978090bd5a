{"type": "record", "name": "AgentReferralPayoutRecord", "namespace": "com.bookiply.datalake.records", "fields": [{"name": "apartment_id", "type": ["null", "long"]}, {"name": "referee_agent_id", "type": ["null", "long"], "default": null}, {"name": "referrer_agent_id", "type": ["null", "long"], "default": null}, {"name": "created_at", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "unit_type_id", "type": ["null", "long"], "default": null}, {"name": "referral_amount", "type": ["null", {"type": "bytes", "logicalType": "decimal", "scale": 2, "precision": 38}], "default": null}, {"name": "type", "type": ["null", "string"], "default": null}]}