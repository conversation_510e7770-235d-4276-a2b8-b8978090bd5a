{"type": "record", "name": "AgentReferralCodeRecord", "namespace": "com.bookiply.datalake.records", "fields": [{"name": "agent_id", "type": "long"}, {"name": "referral_code", "type": ["null", "string"], "default": null}, {"name": "referral_campaign_type", "type": ["null", "string"], "default": null}, {"name": "is_active", "type": ["null", "boolean"], "default": null}, {"name": "created_at", "type": {"type": "long", "logicalType": "timestamp-millis"}}, {"name": "updated_at", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "terms_and_conditions_accepted", "type": ["null", "boolean"], "default": null}, {"name": "marketing_emails_accepted", "type": ["null", "boolean"], "default": null}]}