{"type": "record", "name": "AgentReferralRecord", "namespace": "com.bookiply.datalake.records", "fields": [{"name": "agent_id", "type": "long"}, {"name": "referral_code", "type": ["null", "string"], "default": null}, {"name": "referrer_agent_id", "type": ["null", "long"], "default": null}, {"name": "deleted_at", "type": ["null", {"type": "long", "logicalType": "timestamp-millis"}], "default": null}, {"name": "created_at", "type": {"type": "long", "logicalType": "timestamp-millis"}}]}