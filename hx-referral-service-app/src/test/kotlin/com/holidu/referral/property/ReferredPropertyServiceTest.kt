package com.holidu.referral.property

import com.bookiply.apartment.model.ApartmentTypeEnum
import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.application.property.ReferredPropertyService
import com.holidu.referral.domain.property.PublishedProperty
import com.holidu.referral.legalentity.Address
import com.holidu.referral.legalentity.Billing
import com.holidu.referral.legalentity.Individual
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC

class ReferredPropertyServiceTest @Autowired constructor(
    private val sut: ReferredPropertyService
) : IntegrationTestBase() {

    val referrerLegalEntityId = 201L
    val refereeLegalEntityId = 301L
    val referredUnitTypeId = 2001L
    val referredParentListingId = 2001L
    val nonReferredLegalEntityId = 101L
    val nonReferredUnitTypeId = 1001L
    val nonReferredParentListingId = 1001L

    @Test
    fun `property created in PMS NOT by a referee is skipped`() {
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = nonReferredUnitTypeId,
            refereeLegalEntityId = nonReferredLegalEntityId
        )

        sut.createProperty(nonReferredUnitTypeId)

        referredPropertyRepository.findAll() shouldBe emptyList()
    }


    @Test
    fun `referred property creation processed already should be ignored`() {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        val existingRecord = createdProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = refereeLegalEntityId,
            unitTypeId = referredUnitTypeId,
            parentListingId = referredParentListingId,
        )

        sut.createProperty(referredUnitTypeId)

        val result = referredPropertyRepository.findAll()
        result shouldHaveSize 1
        result[0].id shouldBe existingRecord.id
    }

    @Test
    fun `referred property created in PMS leads to the creation of a referred property`() {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        val referee = createReferee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = referredUnitTypeId,
            parentListingId = referredParentListingId,
            refereeLegalEntityId = refereeLegalEntityId,
            creationDate = THREE_DAYS_AGO
        )

        sut.createProperty(referredUnitTypeId)

        val result = referredPropertyRepository.findByUnitTypeId(unitTypeId = referredUnitTypeId)!!
        result.referrerId shouldBe referrer.id
        result.refereeLegalEntityId shouldBe referee.legalEntityId
        result.creationDate.toLocalDate() shouldBe THREE_DAYS_AGO.toLocalDate()
    }

    @Test
    fun `property published in PMS NOT by a referee is skipped`() {
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = nonReferredUnitTypeId,
            refereeLegalEntityId = nonReferredLegalEntityId,
            published = true,
            firstPublicationDate = NOW
        )

        sut.publishProperty(nonReferredUnitTypeId)

        referredPropertyRepository.findAll() shouldBe emptyList()
    }

    @Test
    fun `referred property publication processed already should be ignored`() {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        val existingRecord = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = refereeLegalEntityId,
            unitTypeId = referredUnitTypeId,
            parentListingId = referredParentListingId,
            publicationDate = OffsetDateTime.now(UTC)
        )

        sut.publishProperty(referredUnitTypeId)

        val result = referredPropertyRepository.findPublishedProperties()
        result shouldHaveSize 1
        result.first().publicationDate shouldBeUpToSeconds existingRecord.publicationDate
        paymentRepository.findByUnitTypeId(referredUnitTypeId) shouldBe null
        mockCommandPublisher.sentCommands shouldBe emptyList()
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `referred property published in PMS leads to the referred property create or update`(
        unitTypeCreationProcessed: Boolean
    ) {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        val referee = createReferee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )

        pmsPropertyExistsWithEditorRelationship(
            <EMAIL>,
            <EMAIL>,
            <EMAIL>,
            ApartmentTypeEnum.APARTMENT,
            THREE_DAYS_AGO,
            true,
            YESTERDAY
        )


        if (unitTypeCreationProcessed) {
            createdProperty(
                referrerId = referrer.id,
                refereeLegalEntityId = refereeLegalEntityId,
                unitTypeId = referredUnitTypeId,
                parentListingId = referredParentListingId,
                creationDate = THREE_DAYS_AGO
            )
        }
        createRewardRule(startDate = THREE_DAYS_AGO, endDate = OffsetDateTime.now(UTC))

        stubLegalEntityResponse(refereeLegalEntityId)

        sut.publishProperty(referredUnitTypeId)

        val result = referredPropertyRepository.findByUnitTypeId(unitTypeId = referredUnitTypeId)!! as PublishedProperty

        result.referrerId shouldBe referrer.id
        result.refereeLegalEntityId shouldBe referee.legalEntityId
        result.unitTypeId shouldBe referredUnitTypeId
        result.creationDate.toLocalDate() shouldBe THREE_DAYS_AGO.toLocalDate()
        result.publicationDate.toLocalDate() shouldBe YESTERDAY.toLocalDate()
    }


    @Test
    fun `if apartment type change from the time of creation to publication it should be updated in the platform`() {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        createReferee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = referredUnitTypeId,
            parentListingId = referredParentListingId,
            propertyType = ApartmentTypeEnum.BOAT,
            creationDate = THREE_DAYS_AGO,
            published = true,
            firstPublicationDate = NOW
        )
        createdProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = refereeLegalEntityId,
            unitTypeId = referredUnitTypeId,
            parentListingId = referredParentListingId,
            creationDate = THREE_DAYS_AGO,
        )

        createRewardRule(startDate = THREE_DAYS_AGO, endDate = OffsetDateTime.now(UTC))

        stubLegalEntityResponse(refereeLegalEntityId)

        sut.publishProperty(referredUnitTypeId)

        val result = referredPropertyRepository.findByUnitTypeId(unitTypeId = referredUnitTypeId)!! as PublishedProperty

        result.propertyType shouldBe ApartmentTypeEnum.BOAT.name
    }

    @Test
    fun `payment is saved on a referred property publication`() {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        createReferee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = referredUnitTypeId,
            parentListingId = referredParentListingId,
            refereeLegalEntityId = refereeLegalEntityId,
            propertyType = ApartmentTypeEnum.BOAT,
            creationDate = THREE_DAYS_AGO.minusDays(5),
            published = true,
            firstPublicationDate = THREE_DAYS_AGO.plusDays(1)
        )

        stubLegalEntityResponse(refereeLegalEntityId)

        createRewardRule(startDate = THREE_DAYS_AGO, endDate = OffsetDateTime.now(UTC), reward = 111)

        sut.publishProperty(referredUnitTypeId)

        val createdReferredProperty = referredPropertyRepository.findByUnitTypeId(unitTypeId = referredUnitTypeId)!!
        val createdPayment = paymentRepository.findByUnitTypeId(createdReferredProperty.unitTypeId)!!

        createdReferredProperty.referrerId shouldNotBe null
        createdPayment shouldNotBe null
        createdPayment.reward shouldBe 111
    }

    private fun stubLegalEntityResponse(refereeLegalEntityId: Long) {
        whenever(legalEntityServiceTokenClient.getLegalEntity(refereeLegalEntityId))
            .thenReturn(
                Individual(
                    id = 23L,
                    isAmbassador = true,
                    firstName = "John",
                    lastName = "Doe",
                    address = Address("Street 1", "123456", "München", "DE"),
                    billing = Billing(null, "DE1234567890")
                )
            )
    }


    private data class PmsUnit(
        val unitTypeId: Long,
        val apartmentType: ApartmentTypeEnum,
        val createdAt: OffsetDateTime,
        val firstPublished: OffsetDateTime? = null
    )

    companion object {
        private val THREE_DAYS_AGO = OffsetDateTime.now(UTC).minusDays(3)
        private val YESTERDAY = OffsetDateTime.now(UTC).minusDays(1)
    }
}