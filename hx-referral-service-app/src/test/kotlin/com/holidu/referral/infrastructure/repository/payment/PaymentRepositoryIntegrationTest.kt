package com.holidu.referral.infrastructure.repository.payment

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.infrastructure.events.datalake.IndexSingleDatalakeRecordCommand
import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.Payment
import com.holidu.referral.payment.PaymentDatalakeRecord
import com.holidu.referral.payment.PaymentState
import com.holidu.referral.referee.Referee
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class PaymentRepositoryIntegrationTest @Autowired constructor(
    private val sut: PaymentRepository
) : IntegrationTestBase() {
    @Test
    fun `when payment is saved, a command to index payment is sent`() {
        val referrer = createReferrer(legalEntityId = 128L)
        val referee = Referee(
            legalEntityId = 931L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        val property = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            parentListingId = 95L,
            unitTypeId = 95L
        )
        val payment = Payment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            unitTypeId = property.unitTypeId,
            creationDate = NOW.minusMinutes(7),
            reward = 75,
            linkedEntityType = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.PENDING
        )

        sut.save(payment)

        mockCommandPublisher.sentCommands shouldContain IndexSingleDatalakeRecordCommand(
            record = PaymentDatalakeRecord.newBuilder().apply {
                    apartmentId = 95L
                    referrerAgentId = 128L
                    refereeAgentId = 931L
                    createdAt = NOW.minusMinutes(7).toInstant()
                    unitTypeId = 95L
                    referralAmount = 75.toBigDecimal()
                    type = "REWARD"
                }.build(),
            streamName = "firehose_bookiply_data_agent_referral_payout"
        )
    }

    @Test
    fun `failure to index payment does NOT block saving payment to database`() {
        val referrer = createReferrer(legalEntityId = 7L)
        val bonus = createBonus(
            referrerId = referrer.id,
            numberOfPayments = 3
        )
        val payment = Payment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            creationDate = NOW.minusHours(2),
            reward = 150,
            linkedEntityType = LinkedEntityType.BONUS,
            bonusId = bonus.id,
            state = PaymentState.PENDING
        )
        mockFirehoseWriter.throwExceptionOnCalls()

        sut.save(payment)
        val result = sut.findByBonusId(bonus.id)!!

        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe 7L
        result.creationDate shouldBe NOW.minusHours(2)
        result.bonusId shouldBe bonus.id
        result.reward shouldBe 150
        result.linkedEntityType shouldBe LinkedEntityType.BONUS
        result.state shouldBe PaymentState.PENDING
    }

    @Test
    fun `failure to send command to index referee does NOT block saving referee to database`() {
        val referrer = createReferrer(legalEntityId = 98L)
        val bonus = createBonus(
            referrerId = referrer.id,
            numberOfPayments = 3
        )
        val payment = Payment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            creationDate = NOW.minusHours(5),
            reward = 300,
            linkedEntityType = LinkedEntityType.BONUS,
            bonusId = bonus.id,
            state = PaymentState.PENDING
        )
        mockCommandPublisher.throwExceptionOnCalls()

        sut.save(payment)
        val result = sut.findByBonusId(bonus.id)!!

        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe 98L
        result.bonusId shouldBe bonus.id
        result.creationDate shouldBe NOW.minusHours(5)
        result.reward shouldBe 300
        result.linkedEntityType shouldBe LinkedEntityType.BONUS
        result.state shouldBe PaymentState.PENDING
    }
}
