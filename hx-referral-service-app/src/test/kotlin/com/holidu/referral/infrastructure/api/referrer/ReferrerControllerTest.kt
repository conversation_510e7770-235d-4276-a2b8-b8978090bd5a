package com.holidu.referral.infrastructure.api.referrer

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.api.referee.RefereeDto
import com.holidu.referral.api.referrer.ReferrerDto
import org.junit.jupiter.api.Test
import org.springframework.test.web.reactive.server.expectBody
import java.time.ZoneOffset

class ReferrerControllerTest : IntegrationTestBase() {
    private val referrerLegalEntityId = 12L
    private val referredByLegalEntityId = 34L

    @Test
    fun `referrer could is a referee in respect to another referrer`() {
        val referredByReferrer = createReferrer(legalEntityId = referredByLegalEntityId)
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        createReferee(
            legalEntityId = referrerLegalEntityId,
            referrerId = referredByReferrer.id,
            referrerLegalEntityId = referredByReferrer.legalEntityId
        )

        webTestClient.get()
            .uri("/v1/referrers/12")
            .exchange()
            .expectStatus().isOk
            .expectBody<ReferrerDto>()
            .isEqualTo(
                ReferrerDto(
                    legalEntityId = referrerLegalEntityId,
                    referralCode = referrer.referralCode,
                    referredByLegalEntityId = referredByLegalEntityId,
                    referees = emptyList()
                )
            )
    }

    @Test
    fun `referrer has multiple referees`() {
        val referrer = createReferrer(legalEntityId = referrerLegalEntityId)
        val referee = createReferee(
            legalEntityId = 56,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )

        webTestClient.get()
            .uri("/v1/referrers/12")
            .exchange()
            .expectStatus().isOk
            .expectBody<ReferrerDto>()
            .isEqualTo(
                ReferrerDto(
                    legalEntityId = referrerLegalEntityId,
                    referralCode = referrer.referralCode,
                    referredByLegalEntityId = null,
                    referees = listOf(
                        RefereeDto(
                            legalEntityId = 56,
                            referrerLegalEntityId = referrer.legalEntityId,
                            creationDate = referee.createdAt.atOffset(ZoneOffset.UTC)
                        )
                    )
                )
            )
    }
}
