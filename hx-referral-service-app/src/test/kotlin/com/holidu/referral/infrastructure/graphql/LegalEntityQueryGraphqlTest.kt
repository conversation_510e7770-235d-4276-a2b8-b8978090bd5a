package com.holidu.referral.infrastructure.graphql

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.bonus.BonusType.TEN_X_BONUS
import com.holidu.referral.legalentity.Billing
import com.holidu.referral.legalentity.Individual
import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.LinkedEntityType.BONUS
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import java.math.BigDecimal
import java.time.Instant
import java.time.Month
import java.time.Month.APRIL
import java.time.Month.MAY
import java.time.OffsetDateTime.now
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit


class LegalEntityQueryGraphqlTest : IntegrationTestBase() {
    private val legalEntityId = 1000L

    @BeforeEach
    fun setUp() {
        whenever(legalEntityUserTokenClient.findLegalEntityId()).thenReturn(1000)
        whenever(legalEntityUserTokenClient.getLegalEntity(any())).thenReturn(
            Individual(
                id = legalEntityId,
                firstName = "Max",
                lastName = "Mustermann",
                address = null,
                billing = Billing(null, null),
                isAmbassador = true
            )
        )
    }

    @Test
    fun `query legal entity names`() {
        createReferrer(legalEntityId = legalEntityId)
        val query = """
            query GetLegalEntity {
              getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                id
                ... on Individual {
                  firstName
                  lastName
                }
                referrer {
                  id
                }
              }
            }
        """.trimIndent()

        webGraphQlTester.document(query)
            .execute()
            .path("getLegalEntity.firstName").entity(String::class.java).isEqualTo("Max")
            .path("getLegalEntity.lastName").entity(String::class.java).isEqualTo("Mustermann")
    }

    @Test
    fun `referrers total reward is calculated on demand`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        createPayment(referrerId = referrer.id, reward = 500, unitTypeId = 102L)
        val query = """
            query GetLegalEntity {
              getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                referrer {
                  totalReward
                }
              }
            }
        """.trimIndent()

        webGraphQlTester.document(query)
            .execute()
            .path("data.getLegalEntity.referrer.totalReward").entity(Int::class.java).isEqualTo(500)
    }

    @Test
    fun `referrers total reward this month`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        createPayment(referrerId = referrer.id, reward = 500, creationDate = now(), unitTypeId = 102L)
        createPayment(referrerId = referrer.id, reward = 100, creationDate = now(), unitTypeId = 103L)
        createPayment(referrerId = referrer.id, reward = 1000, creationDate = now().minusMonths(1), unitTypeId = 104L)
        createPayment(referrerId = referrer.id, reward = 2000, creationDate = now().plusMonths(1), unitTypeId = 105L)
        createPayment(
            referrerId = referrer.id,
            reward = 2000,
            creationDate = now(),
            deletedAt = Instant.now(),
            unitTypeId = 102L
        )
        val query = """
            query GetLegalEntity {
              getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                referrer {
                  totalMonthPayment
                }
              }
            }
        """.trimIndent()

        webGraphQlTester.document(query)
            .execute()
            .path("data.getLegalEntity.referrer.totalMonthPayment").entity(Int::class.java).isEqualTo(600)
    }

    @Test
    fun `referred properties are returned successfully`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)

        val firstReferee = createReferee(
            legalEntityId = 2000,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            name = "firstReferee"
        )
        val firstProperty = createdProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = firstReferee.legalEntityId,
            creationDate = NOW.minusMonths(1)
        )
        val secondReferee = createReferee(
            legalEntityId = 2001,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            name = "secondReferee"
        )
        val secondProperty = createdProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = secondReferee.legalEntityId,
            creationDate = NOW.minusDays(1)
        )
        val lastReferee = createReferee(
            legalEntityId = 2002,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            name = "lastReferee"
        )
        val lastProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = lastReferee.legalEntityId,
            creationDate = NOW
        )
        createPayment(
            referrerId = referrer.id,
            reward = 75,
            unitTypeId = lastProperty.unitTypeId,
            type = LinkedEntityType.REFERRED_PROPERTY
        )

        val query = """
            query GetLegalEntity {
                getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                    id
                    referrer {
                        id
                        referredProperties(offset: 0, limit: 2) {
                            referredProperties {
                                id
                                propertyStatus
                                creationDate
                                publicationDate
                                reward
                                referee {
                                    id
                                    info
                                }
                            }
                            pageInfo {
                                total
                                currentOffset
                                limit
                                hasNextPage
                                hasPreviousPage
                            }
                        }
                    }
                }
            }
        """.trimIndent()


        val executionResults = webGraphQlTester.document(query)
            .execute()

        val referredProperties = executionResults
            .path("data.getLegalEntity.referrer.referredProperties.referredProperties")
            .entityList(ReferredProperty::class.java)
            .get()

        val pageInfo = executionResults
            .path("data.getLegalEntity.referrer.referredProperties.pageInfo")
            .entity(OffsetPageInfo::class.java)
            .get()

        referredProperties.size shouldBe 2
        referredProperties.map { it.referee.id } shouldBe
                listOf(
                    lastProperty.refereeLegalEntityId.toString(),
                    secondProperty.refereeLegalEntityId.toString()
                )
        referredProperties.map { it.referee.info } shouldBe
                listOf(
                    lastReferee.name,
                    secondReferee.name
                )
        pageInfo.total shouldBe 3
        pageInfo.currentOffset shouldBe 0
        pageInfo.limit shouldBe 2
        pageInfo.hasNextPage shouldBe true
        pageInfo.hasPreviousPage shouldBe false
    }


    @Test
    fun `bonuses are returned successfully`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        val achievedAt = now(UTC).truncatedTo(ChronoUnit.MICROS)

        val firstBonus =
            createBonus(referrerId = referrer.id, reward = 100, numberOfPayments = 3, achievedAt = achievedAt)
        val secondBonus = createBonus(
            referrerId = referrer.id,
            reward = 200,
            numberOfPayments = 10,
            type = TEN_X_BONUS,
            achievedAt = achievedAt
        )
        createBonus(
            referrerId = referrer.id,
            reward = 300,
            numberOfPayments = 23,
            achievedAt = achievedAt.minusMonths(1)
        )

        val query = """
            query GetLegalEntity {
                getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                    id
                    referrer {
                        id
                        bonuses(limit: 2, offset: 0) {
                            bonuses {
                                id
                                type
                                poEditorDescriptionKey
                                creationDate
                                reward
                            }
                            pageInfo {
                                total
                                limit
                                hasNextPage
                                hasPreviousPage
                            }
                        }
                    }
                }
            }
        """.trimIndent()

        val executionResults = webGraphQlTester.document(query)
            .execute()


        val bonuses = executionResults
            .path("data.getLegalEntity.referrer.bonuses.bonuses")
            .entityList(Bonus::class.java)
            .get()

        val pageInfo = executionResults
            .path("data.getLegalEntity.referrer.bonuses.pageInfo")
            .entity(OffsetPageInfo::class.java)
            .get()

        bonuses.size shouldBe 2
        bonuses shouldContainExactlyInAnyOrder listOf(
            Bonus(
                id = firstBonus.id.toString(),
                type = "ENDS_WITH_3",
                poEditorDescriptionKey = "to be removed",
                creationDate = firstBonus.achievedAt,
                reward = 100.0.toBigDecimal()
            ),
            Bonus(
                id = secondBonus.id.toString(),
                type = "MULTIPLE_OF_10",
                poEditorDescriptionKey = "to be removed",
                creationDate = secondBonus.achievedAt,
                reward = 200.0.toBigDecimal()
            ),
        )
        pageInfo.total shouldBe 3
        pageInfo.currentOffset shouldBe 0
        pageInfo.limit shouldBe 2
        pageInfo.hasNextPage shouldBe true
        pageInfo.hasPreviousPage shouldBe false
    }

    @Test
    fun `payments are returned successfully`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        createPayment(referrerId = referrer.id, reward = 75, creationDate = APRIL.toOffsetDateTime(), unitTypeId = 102L)
        createPayment(referrerId = referrer.id, reward = 75, creationDate = MAY.toOffsetDateTime(), unitTypeId = 103L)
        createPayment(
            referrerId = referrer.id,
            type = BONUS,
            bonusId = 1,
            unitTypeId = null,
            reward = 100,
            creationDate = MAY.toOffsetDateTime()
        )
        val query = """
            query GetLegalEntity {
                getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                    referrer {
                        payments(limit: 20, offset: 0) {
                            monthlyPayments {
                                id
                                date
                                month
                                year
                                reward
                                downloadUrl
                            }
                            pageInfo {
                                total
                                limit
                                hasNextPage
                                hasPreviousPage
                            }
                        }
                    }
                }
            }
        """.trimIndent()

        val result = webGraphQlTester.document(query)
            .execute()

        val monthlyPayments = result
            .path("data.getLegalEntity.referrer.payments.monthlyPayments")
            .entityList(MonthlyPayment::class.java)
            .get()
        monthlyPayments shouldContainExactlyInAnyOrder listOf(
            MonthlyPayment(
                id = "2025_4",
                date = APRIL.toOffsetDateTime().toString(),
                month = 4,
                year = 2025,
                reward = BigDecimal.valueOf(75.0),
                downloadUrl = "/api/v1/invoice/download?month=4&year=2025"
            ),
            MonthlyPayment(
                id = "2025_5",
                date = MAY.toOffsetDateTime().toString(),
                month = 5,
                year = 2025,
                reward = BigDecimal.valueOf(175.0),
                downloadUrl = "/api/v1/invoice/download?month=5&year=2025"
            ),
        )

        val pageInfo = result
            .path("data.getLegalEntity.referrer.payments.pageInfo")
            .entity(OffsetPageInfo::class.java)
            .get()
        pageInfo shouldBe OffsetPageInfo(
            total = 2,
            currentOffset = 0,
            limit = 20,
            hasNextPage = false,
            hasPreviousPage = false
        )
    }

    @Test
    fun `referrers status`() {
        createReferrer(legalEntityId = legalEntityId, termsAndConditionsAccepted = false)
        val query = """
            query GetLegalEntity {
              getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                id
                referrer {
                  id
                  referrerStatus
                }
              }
            }
        """.trimIndent()

        webGraphQlTester.document(query)
            .execute()
            .path("data.getLegalEntity.referrer.referrerStatus").entity(ReferrerStatus::class.java)
            .isEqualTo(ReferrerStatus.TERMS_AND_CONDITIONS_MISSING)
    }

    @Test
    fun `total referred properties are properties that a published`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        createdProperty(referrerId = referrer.id)
        publishedProperty(referrerId = referrer.id)
        val query = """
            query GetLegalEntity {
              getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                referrer {
                  totalReferredProperties
                  totalPendingReferredProperties
                }
              }
            }
        """.trimIndent()

        webGraphQlTester.document(query)
            .execute()
            .path("data.getLegalEntity.referrer.totalReferredProperties").entity(Int::class.java).isEqualTo(1)
            .path("data.getLegalEntity.referrer.totalPendingReferredProperties").entity(Int::class.java).isEqualTo(1)
    }

    @Test
    fun `pagination works correctly for bonuses`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        Month.entries.forEach { month ->
            createBonus(
                referrerId = referrer.id,
                reward = 100,
                numberOfPayments = 3 + month.value,
                achievedAt = month.toOffsetDateTime()
            )
        }
        val query = """
            query GetLegalEntity {
                getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                    id
                    referrer {
                        id
                        bonuses(limit: 10, offset: 10) {
                            bonuses {
                                id
                                type
                                poEditorDescriptionKey
                                creationDate
                                reward
                            }
                            pageInfo {
                                total
                                currentOffset
                                limit
                                hasNextPage
                                hasPreviousPage
                            }
                        }
                    }
                }
            }
        """.trimIndent()
        val executionResults = webGraphQlTester.document(query)
            .execute()
        val bonusResults = executionResults
            .path("data.getLegalEntity.referrer.bonuses.bonuses")
            .entityList(Bonus::class.java)
            .get()
        val pageInfo = executionResults
            .path("data.getLegalEntity.referrer.bonuses.pageInfo")
            .entity(OffsetPageInfo::class.java)
            .get()

        bonusResults.size shouldBe 2
        pageInfo.total shouldBe 12
        pageInfo.currentOffset shouldBe 10
        pageInfo.limit shouldBe 10
        pageInfo.hasNextPage shouldBe false
        pageInfo.hasPreviousPage shouldBe true
    }

    @Test
    fun `pagination works correctly for payments`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        // this creates 12 monthly payment
        Month.entries.forEach { month ->
            createPayment(
                referrerId = referrer.id,
                reward = 75,
                creationDate = month.toOffsetDateTime(),
                unitTypeId = 101L + month.value
            )
        }
        val query = """
            query GetLegalEntity {
                getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                    referrer {
                        payments(limit: 10, offset: 10) {
                            monthlyPayments {
                                id
                                date
                                month
                                year
                                reward
                                downloadUrl
                            }
                            pageInfo {
                                total
                                currentOffset
                                limit
                                hasNextPage
                                hasPreviousPage
                            }
                        }
                    }
                }
            }
        """.trimIndent()

        val result = webGraphQlTester.document(query)
            .execute()
        val monthlyPayments = result
            .path("data.getLegalEntity.referrer.payments.monthlyPayments")
            .entityList(MonthlyPayment::class.java)
            .get()
        val pageInfo = result
            .path("data.getLegalEntity.referrer.payments.pageInfo")
            .entity(OffsetPageInfo::class.java)
            .get()

        monthlyPayments.size shouldBe 2
        pageInfo.total shouldBe 12
        pageInfo.currentOffset shouldBe 10
        pageInfo.limit shouldBe 10
        pageInfo.hasNextPage shouldBe false
        pageInfo.hasPreviousPage shouldBe true
    }

    @Test
    fun `pagination works correctly for referred properties`() {
        val referrer = createReferrer(legalEntityId = legalEntityId)
        val referee = createReferee(
            legalEntityId = 2000,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        Month.entries.forEach { month ->
            publishedProperty(
                referrerId = referrer.id,
                refereeLegalEntityId = referee.legalEntityId,
                creationDate = month.toOffsetDateTime(),
                publicationDate = month.toOffsetDateTime(),
                unitTypeId = month.value.toLong()
            )
        }
        val query = """
            query GetLegalEntity {
                getLegalEntity(externalUserId: "$EXTERNAL_USER_ID") {
                    id
                    referrer {
                        id
                        referredProperties(offset: 10, limit: 10) {
                            referredProperties {
                                id
                                propertyStatus
                                creationDate
                                publicationDate
                                reward
                                referee {
                                    id
                                    info
                                }
                            }
                            pageInfo {
                                total
                                currentOffset
                                limit
                                hasNextPage
                                hasPreviousPage
                            }
                        }
                    }
                }
            }
        """.trimIndent()

        val executionResults = webGraphQlTester.document(query)
            .execute()
        val referredProperties = executionResults
            .path("data.getLegalEntity.referrer.referredProperties.referredProperties")
            .entityList(ReferredProperty::class.java)
            .get()
        val pageInfo = executionResults
            .path("data.getLegalEntity.referrer.referredProperties.pageInfo")
            .entity(OffsetPageInfo::class.java)
            .get()

        referredProperties.size shouldBe 2
        pageInfo.total shouldBe 12
        pageInfo.currentOffset shouldBe 10
        pageInfo.limit shouldBe 10
        pageInfo.hasNextPage shouldBe false
        pageInfo.hasPreviousPage shouldBe true
    }

    companion object {
        private const val EXTERNAL_USER_ID = "52bab3bb-258b-4fde-83c6-47de29bcd45d"
    }
}