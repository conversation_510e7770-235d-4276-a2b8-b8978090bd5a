package com.holidu.referral.infrastructure.repository.referrer

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.infrastructure.events.datalake.IndexSingleDatalakeRecordCommand
import com.holidu.referral.referrer.Referrer
import com.holidu.referral.referrer.ReferrerDatalakeRecord
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class ReferrerRepositoryIntegrationTest @Autowired constructor(
    private val sut: ReferrerRepository
) : IntegrationTestBase() {
    @Test
    fun `when referrer is saved, a command to index referee is sent`() {
        val referrer = Referrer(
            legalEntityId = 567L,
            referralCode = "AB3409DC",
            marketingEmailsAccepted = true,
            termsAndConditionsAccepted = true
        )

        val result = sut.save(referrer)

        mockCommandPublisher.sentCommands shouldContain IndexSingleDatalakeRecordCommand(
            record = ReferrerDatalakeRecord.newBuilder().apply {
                agentId = 567L
                referralCode = "AB3409DC"
                referralCampaignType = "REFERRAL_PROGRAM_V1"
                isActive = true
                createdAt = result.createdAt
                updatedAt = result.updatedAt
                termsAndConditionsAccepted = true
                marketingEmailsAccepted = true
            }.build(),
            streamName = "firehose_bookiply_data_agent_referral_code"
        )
    }

    @Test
    fun `failure to index referrer does NOT block saving referrer to database`() {
        val referrer = Referrer(
            legalEntityId = 891L,
            referralCode = "AB37HG99",
            marketingEmailsAccepted = false,
            termsAndConditionsAccepted = true
        )
        mockFirehoseWriter.throwExceptionOnCalls()

        sut.save(referrer)
        val result = sut.findByLegalEntityId(891L)!!

        result.legalEntityId shouldBe 891L
        result.referralCode shouldBe "AB37HG99"
        result.marketingEmailsAccepted shouldBe false
        result.termsAndConditionsAccepted shouldBe true
    }

    @Test
    fun `failure to send command to index referrer does NOT block saving referrer to database`() {
        val referrer = Referrer(
            legalEntityId = 17L,
            referralCode = "ABC901TR",
            marketingEmailsAccepted = false,
            termsAndConditionsAccepted = true
        )
        mockCommandPublisher.throwExceptionOnCalls()

        sut.save(referrer)
        val result = sut.findByLegalEntityId(17L)!!

        result.legalEntityId shouldBe 17L
        result.referralCode shouldBe "ABC901TR"
        result.marketingEmailsAccepted shouldBe false
        result.termsAndConditionsAccepted shouldBe true
    }
}
