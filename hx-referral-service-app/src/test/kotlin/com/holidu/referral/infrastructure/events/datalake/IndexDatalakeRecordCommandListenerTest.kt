package com.holidu.referral.infrastructure.events.datalake

import com.holidu.referral.IntegrationTestBase
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class IndexDatalakeRecordCommandListenerTest @Autowired constructor(
    private val sut: IndexDatalakeRecordCommandListener
) : IntegrationTestBase() {
    @Test
    fun `referrer is indexed on index command`() {
        val referrer = createReferrer(
                legalEntityId = 567L,
                referralCode = "AB3409DC",
                termsAndConditionsAccepted = true,
                marketingEmailsAccepted = true
            )

        sut.onIndexSingleDatalakeRecordCommand(
            IndexSingleDatalakeRecordCommand(
                record = referrer.toDatalakeRecord(),
                streamName = referrer.firehoseStreamName
            )
        )

        mockFirehoseWriter.sentRecords shouldContain """
            {
              "agent_id": 567,
              "referral_code": "AB3409DC",
              "referral_campaign_type": "REFERRAL_PROGRAM_V1",
              "is_active": true,
              "created_at": ${referrer.createdAt.toEpochMilli()},
              "updated_at": ${referrer.updatedAt.toEpochMilli()},
              "terms_and_conditions_accepted": true,
              "marketing_emails_accepted": true
            }
        """.onlyAlphaNumeric()
    }

    @Test
    fun `referee is indexed on index command`() {
        val referrer = createReferrer(legalEntityId = 123L)
        val referee = createReferee(
            legalEntityId = 256L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )

        sut.onIndexSingleDatalakeRecordCommand(
            IndexSingleDatalakeRecordCommand(
                record = referee.toDatalakeRecord(),
                streamName = referee.firehoseStreamName
            )
        )

        mockFirehoseWriter.sentRecords shouldContain """
            {
                "agent_id": 256,
                "referral_code": null,
                "referrer_agent_id": 123,
                "deleted_at": null,
                "created_at": ${referee.createdAt.toEpochMilli()}
            }
        """.onlyAlphaNumeric()
    }

    @Test
    fun `payment is indexed on index command`() {
        val referrer = createReferrer(legalEntityId = 673L)
        val referee = createReferee(
            legalEntityId = 901L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        val property = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            parentListingId = 128L,
            unitTypeId = 128L
        )
        val payment = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            reward = 150,
            unitTypeId = property.unitTypeId,
            creationDate = NOW.minusDays(3)
        )

        sut.onIndexSingleDatalakeRecordCommand(
            IndexSingleDatalakeRecordCommand(
                record = payment.toDatalakeRecord(),
                streamName = payment.firehoseStreamName
            )
        )

        mockFirehoseWriter.sentRecords shouldContain """
            {
                "apartment_id": 128,
                "referee_agent_id": 901,
                "referrer_agent_id": 673,
                "created_at": ${NOW.minusDays(3).toInstant().toEpochMilli()},
                "unit_type_id": 128,
                "referral_amount": "150.00",
                "type": "REWARD"
}
        """.onlyAlphaNumeric()
    }

    @Test
    fun `batch indexing works`() {
        val referrerOne = createReferrer(legalEntityId = 12L)
        val referrerTwo = createReferrer(legalEntityId = 57L)
        val referrerThree = createReferrer(legalEntityId = 938L)

        sut.onIndexBatchDatalakeRecordsCommand(
            IndexBatchDatalakeRecordsCommand(
                records = listOf(
                    referrerOne.toDatalakeRecord(),
                    referrerTwo.toDatalakeRecord(),
                    referrerThree.toDatalakeRecord()
                ),
                streamName = referrerOne.firehoseStreamName
            )
        )

        mockFirehoseWriter.sentRecords shouldHaveSize 3
        mockFirehoseWriter.sentRecords shouldHaveReferrerWithId 12L
        mockFirehoseWriter.sentRecords shouldHaveReferrerWithId 57L
        mockFirehoseWriter.sentRecords shouldHaveReferrerWithId 938L
    }

    companion object {
        private fun String.onlyAlphaNumeric(): String =
            this.trimIndent()
                .replace(" ", "")
                .replace("\t", "")
                .replace("\n", "")

        private infix fun List<String>.shouldHaveReferrerWithId(id: Long) {
            this.filter { it.contains("\"agent_id\":$id") } shouldNotBe null
        }
    }
}