package com.holidu.referral.infrastructure.repository.referee

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.infrastructure.events.datalake.IndexSingleDatalakeRecordCommand
import com.holidu.referral.referee.Referee
import com.holidu.referral.referee.RefereeDatalakeRecord
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

class RefereeRepositoryIntegrationTest @Autowired constructor(
    private val sut: RefereeRepository
) : IntegrationTestBase() {
    @Test
    fun `when referee is saved, a command to index referee is sent`() {
        val referrer = createReferrer(legalEntityId = 128L)
        val referee = Referee(
            legalEntityId = 931L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )

        val result = sut.save(referee)

        mockCommandPublisher.sentCommands shouldContain IndexSingleDatalakeRecordCommand(
            records = listOf(
                RefereeDatalakeRecord.newBuilder().apply {
                    agentId = 931L
                    referralCode = null
                    referrerAgentId = 128L
                    createdAt = result.createdAt
                    deletedAt = null
                }.build()
            ),
            streamName = "firehose_bookiply_data_agent_referral"
        )
    }

    @Test
    fun `failure to index referee does NOT block saving referee to database`() {
        val referrer = createReferrer(legalEntityId = 291L)
        val referee = Referee(
            legalEntityId = 329L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        mockFirehoseWriter.throwExceptionOnCalls()

        sut.save(referee)
        val result = sut.findByLegalEntityId(329L)!!

        result.legalEntityId shouldBe 329L
        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe referrer.legalEntityId
    }

    @Test
    fun `failure to send command to index referee does NOT block saving referee to database`() {
        val referrer = createReferrer(legalEntityId = 16L)
        val referee = Referee(
            legalEntityId = 84L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        mockCommandPublisher.throwExceptionOnCalls()

        sut.save(referee)
        val result = sut.findByLegalEntityId(84L)!!

        result.legalEntityId shouldBe 84L
        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe referrer.legalEntityId
    }
}
