package com.holidu.referral.infrastructure.api.referee

import com.bookiply.apartment.dto.UnitTypeDto
import com.bookiply.apartment.model.ApartmentTypeEnum
import com.bookiply.collaborator.dto.AgentParentListingRelationDto
import com.bookiply.rest.dto.SearchResponse
import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.api.referee.RefereeDto
import com.holidu.referral.domain.property.PublishedProperty
import com.holidu.referral.legalentity.Billing
import com.holidu.referral.legalentity.Individual
import com.holidu.referral.pms.client.ParentListingGeneralInfoResponseDto
import com.holidu.referral.pms.client.UnitTypeGeneralInfoResponseDto
import com.holidu.referral.pms.client.UnitTypePublishInfoDto
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.whenever
import org.springframework.security.test.context.support.WithMockUser
import java.time.LocalDateTime
import java.time.OffsetDateTime

class RefereeControllerTest : IntegrationTestBase() {

    @BeforeEach
    fun setUp() {
        whenever(legalEntityServiceTokenClient.getLegalEntity(any())).thenReturn(
            Individual(
                id = 34L,
                isAmbassador = false,
                firstName = "John",
                lastName = "Doe",
                address = null,
                billing = Billing(null, "DE1234567890")
            )
        )
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `referee is created for a referrer`() {
        val referrer = createReferrer(legalEntityId = 12)
        refereeHasNoPmsProperties()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 34,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful
        val result = refereeRepository.findByLegalEntityId(34)!!

        result.legalEntityId shouldBe 34
        result.referrerId shouldBe referrer.id
        result.name shouldBe "John D"
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `new referrer is created if missing when creating a referee`() {
        refereeHasNoPmsProperties()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 34,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful

        val result = referrerRepository.findByLegalEntityId(12)!!

        result.legalEntityId shouldBe 12
        result.marketingEmailsAccepted shouldBe false
        result.termsAndConditionsAccepted shouldBe false
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `bad request is returned if referee exists and belongs to a different referrer`() {
        val referrer = createReferrer(legalEntityId = 12)
        createReferee(
            legalEntityId = 34,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 34,
                    referrerLegalEntityId = 89,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is4xxClientError
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `all referred properties are created after referee creation`() {
        val referrer = createReferrer(legalEntityId = 12)
        val firstPublished = LocalDateTime.parse("2025-04-01T00:00:00")
        val parentListingId: Long = 12345
        val unitTypeId: Long = 67890
        refereeWithASinglePublishedProperty(parentListingId, unitTypeId, firstPublished)
        createRewardRule()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 34,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful

        val result = referredPropertyRepository.findAll()

        result.size shouldBe 1
        result.first().unitTypeId shouldBe unitTypeId
        result.first().parentListingId shouldBe parentListingId
        result.first().referrerId shouldBe referrer.id
        result.first().refereeLegalEntityId shouldBe 34
        (result.first() as PublishedProperty).publicationDate shouldBe OffsetDateTime.parse("2025-03-31T22:00Z")
        (result.first() as PublishedProperty).propertyType shouldBe "APARTMENT"
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `properties published before 2025-04-01 are ignored`() {
        val firstPublished = LocalDateTime.parse("2024-03-31T00:00:00")
        val parentListingId: Long = 12345
        val unitTypeId: Long = 67890
        refereeWithASinglePublishedProperty(parentListingId, unitTypeId, firstPublished)

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 34,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful

        val result = referredPropertyRepository.findAll()

        result.size shouldBe 0
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `payments and bonuses are created after referee creation`() {
        val firstPublished = LocalDateTime.parse("2025-04-01T00:00:00")
        val parentListingId: Long = 12345
        val unitTypeId: Long = 67890
        refereeWithASinglePublishedProperty(parentListingId, unitTypeId, firstPublished)
        createRewardRule()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 34,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful

        val result = paymentRepository.findAll()

        result.size shouldBe 1
        result.first().refereeLegalEntityId shouldBe 34
        result.first().unitTypeId shouldBe unitTypeId
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `referee creation updates properties if referee already exists`() {
        val referrer = createReferrer(legalEntityId = 12)
        val refereeLegalEntityId: Long = 34
        createReferee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        val creationDate = OffsetDateTime.parse("2125-04-01T12:00:00+00:00")
        val publicationDate = OffsetDateTime.parse("2125-05-01T12:00:00+00:00")
        val createdProperty = createdProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = refereeLegalEntityId,
            unitTypeId = 78,
            parentListingId = 78,
            creationDate = creationDate
        )
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = 78,
            parentListingId = 78,
            refereeLegalEntityId = refereeLegalEntityId,
            published = true,
            creationDate = creationDate,
            firstPublicationDate = publicationDate
        )
        createRewardRule()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = refereeLegalEntityId,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful

        val result = referredPropertyRepository.findAll()
        result.size shouldBe 1
        result.first() shouldBe PublishedProperty(
            id = createdProperty.id,
            referrerId = referrer.id,
            refereeLegalEntityId = refereeLegalEntityId,
            unitTypeId = 78,
            parentListingId = 78,
            creationDate = creationDate,
            propertyType = ApartmentTypeEnum.APARTMENT.name,
            publicationDate = publicationDate
        )
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `referee creation does not create a second payment if property is already published`() {
        val referrer = createReferrer(legalEntityId = 12)
        val refereeLegalEntityId: Long = 34
        createReferee(
            legalEntityId = refereeLegalEntityId,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        val creationDate = OffsetDateTime.parse("2125-04-01T12:00:00+00:00")
        val publicationDate = OffsetDateTime.parse("2125-05-01T12:00:00+00:00")
        publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = refereeLegalEntityId,
            unitTypeId = 78,
            parentListingId = 78,
            creationDate = creationDate,
            publicationDate = publicationDate
        )
        pmsPropertyExistsWithEditorRelationship(
            unitTypeId = 78,
            parentListingId = 78,
            refereeLegalEntityId = refereeLegalEntityId,
            published = true,
            creationDate = creationDate,
            firstPublicationDate = publicationDate
        )
        createRewardRule()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = refereeLegalEntityId,
                    referrerLegalEntityId = 12,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful

        val result = paymentRepository.findAll()

        result.size shouldBe 0
    }

    @Test
    @WithMockUser(authorities = ["ACCOUNT_MANAGEMENT"])
    fun `account managers can create referees`() {
        createReferrer(legalEntityId = 202)
        refereeHasNoPmsProperties()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 101,
                    referrerLegalEntityId = 202,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful
    }

    @Test
    @WithMockUser(authorities = ["SALES_MANAGEMENT"])
    fun `sales managers can create referees`() {
        createReferrer(legalEntityId = 202)
        refereeHasNoPmsProperties()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 101,
                    referrerLegalEntityId = 202,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful
    }

    @Test
    @WithMockUser(authorities = ["AREA_LEADS"])
    fun `area leads can create referees`() {
        createReferrer(legalEntityId = 202)
        refereeHasNoPmsProperties()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 101,
                    referrerLegalEntityId = 202,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .is2xxSuccessful
    }

    @Test
    @WithMockUser()
    fun `otherwise referees creation is forbidden`() {
        createReferrer(legalEntityId = 202)
        refereeHasNoPmsProperties()

        webTestClient.post().uri("/v1/referees")
            .bodyValue(
                RefereeDto(
                    legalEntityId = 101,
                    referrerLegalEntityId = 202,
                    creationDate = OffsetDateTime.now()
                )
            )
            .exchange()
            .expectStatus()
            .isForbidden
    }

    private fun refereeHasNoPmsProperties() {
        whenever(unitTypeAgentClient.findAgentParentListingRelation(any())).thenReturn(
            SearchResponse(listOf(), 0)
        )
    }

    private fun refereeWithASinglePublishedProperty(
        parentListingId: Long,
        unitTypeId: Long,
        firstPublished: LocalDateTime?
    ) {
        whenever(unitTypeAgentClient.findAgentParentListingRelation(any())).thenReturn(
            SearchResponse(listOf(AgentParentListingRelationDto.builder().parentListingId(parentListingId).build()), 1)
        )
        whenever(unitTypeClient.findUnitTypes(any())).thenReturn(
            SearchResponse(
                listOf(
                    UnitTypeDto.builder()
                        .id(unitTypeId)
                        .parentListingId(parentListingId)
                        .createdAt(OffsetDateTime.now())
                        .published(true)
                        .pmcApartmentType(ApartmentTypeEnum.APARTMENT.toWrapper())
                        .build()
                ), 1
            )
        )
        whenever(parentListingGeneralInfoClient.getGeneralInfo(any())).thenReturn(
            ParentListingGeneralInfoResponseDto(
                id = parentListingId,
                listOf(
                    UnitTypeGeneralInfoResponseDto(
                        id = unitTypeId,
                        apartmentType = ApartmentTypeEnum.APARTMENT,
                        createdAt = OffsetDateTime.now(),
                        unitTypePublishInfoDto = UnitTypePublishInfoDto(firstPublished = firstPublished)
                    )
                )
            )
        )
    }
}
