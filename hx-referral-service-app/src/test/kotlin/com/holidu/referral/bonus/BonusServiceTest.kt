package com.holidu.referral.bonus

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.payment.LinkedEntityType
import com.holidu.referral.payment.LinkedEntityType.REFERRED_PROPERTY
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import java.time.Instant
import java.time.OffsetDateTime

class BonusServiceTest @Autowired constructor(
    private val sut: BonusService
) : IntegrationTestBase() {

    val PAGE_SIZE = 20
    val PAGE_NO = 0

    @Test
    fun `bonuses are fetched correctly for a particular referrer`() {
        val referrer = createReferrer()
        val bonus = createBonus(referrerId = referrer.id, numberOfPayments = 3)
        createBonus(referrerId = randomReferrerId(), type = BonusType.TEN_X_BONUS, numberOfPayments = 10)

        val result = sut.getBonusesForReferrer(referrerId = referrer.id, PAGE_NO, PAGE_SIZE).content

        result shouldHaveSize 1
        result[0].id shouldBe bonus.id
    }

    @Test
    fun `deleted bonuses are ignored`() {
        val referrer = createReferrer(legalEntityId = 156L)
        createBonus(referrerId = referrer.id, numberOfPayments = 3, deletedAt = Instant.now())

        val result = bonusRepository.findByReferrerId(referrer.id)

        result.size shouldBe 0
    }

    @Test
    fun `REFERRED_PROPERTIES_PUBLISHED_ENDS_WITH_3 bonus is achieved by referrer if he has 3 payments for the referred published properties`() {
        val referrer = createReferrer()
        repeat(4) { index ->
            createPayment(referrerId = referrer.id, type = REFERRED_PROPERTY, unitTypeId = 102L + index)
        }
        val payment = createPayment(
            referrerId = referrer.id,
            type = REFERRED_PROPERTY,
            creationDate = OffsetDateTime.parse("2125-04-01T12:00:00+00:00"),
            unitTypeId = 202L
        )

        sut.recalculateBonuses(
            recalculateBonusesCommand = RecalculateBonusesCommand(referrerId = referrer.id)
        )
        val result = bonusRepository.findByReferrerId(
            referrer.id,
            PageRequest.of(PAGE_NO, PAGE_SIZE, Sort.by("createdAt").descending())
        ).content

        result shouldHaveSize 1
        result[0].type shouldBe BonusType.THREE_X_BONUS
        result[0].reward shouldBe 100
        result[0].referrerId shouldBe referrer.id
        result[0].achievedAt shouldBeUpToSeconds payment.creationDate
    }

    @Test
    fun `REFERRED_PROPERTIES_PUBLISHED_ENDS_WITH_3 bonus is achieved for the 2nd time by referrer if he has 13 payments for the referred published properties`() {
        val referrer = createReferrer()
        repeat(13) { index ->
            createPayment(referrerId = referrer.id, type = REFERRED_PROPERTY, unitTypeId = 102L + index)
        }

        sut.recalculateBonuses(
            recalculateBonusesCommand = RecalculateBonusesCommand(referrerId = referrer.id)
        )
        val result = bonusRepository.findByReferrerId(
            referrer.id,
            PageRequest.of(PAGE_NO, PAGE_SIZE, Sort.by("createdAt").descending())
        ).content

        result.filter { it.type == BonusType.THREE_X_BONUS } shouldHaveSize 2
    }

    @Test
    fun `REFERRED_PROPERTIES_PUBLISHED_MULTIPLE_OF_10 bonus is achieved by referrer if he has 10 payments for the referred published properties`() {
        val referrer = createReferrer()
        repeat(10) { index ->
            createPayment(referrerId = referrer.id, type = REFERRED_PROPERTY, unitTypeId = 102L + index)
        }

        sut.recalculateBonuses(
            recalculateBonusesCommand = RecalculateBonusesCommand(referrerId = referrer.id)
        )
        val result = bonusRepository.findByReferrerId(
            referrer.id,
            PageRequest.of(PAGE_NO, PAGE_SIZE, Sort.by("createdAt").descending())
        ).content

        result.filter { it.type == BonusType.TEN_X_BONUS } shouldHaveSize 1
        result.first { it.type == BonusType.TEN_X_BONUS }.reward shouldBe 1000
    }

    @Test
    fun `REFERRED_PROPERTIES_PUBLISHED_MULTIPLE_OF_10 bonus is achieved for the 2nd time by referrer if he has 20 payments for the referred published properties`() {
        val referrer = createReferrer()
        repeat(20) { index ->
            createPayment(referrerId = referrer.id, type = REFERRED_PROPERTY, unitTypeId = 102L + index)
        }

        sut.recalculateBonuses(
            recalculateBonusesCommand = RecalculateBonusesCommand(referrerId = referrer.id)
        )
        val result = bonusRepository.findByReferrerId(
            referrer.id,
            PageRequest.of(PAGE_NO, PAGE_SIZE, Sort.by("createdAt").descending())
        ).content

        result.filter { it.type == BonusType.TEN_X_BONUS } shouldHaveSize 2
    }

    @Test
    fun `payment is saved when the bonus is created for a referrer`() {
        val referrer = createReferrer()
        repeat(3) { index ->
            createPayment(referrerId = referrer.id, type = REFERRED_PROPERTY, unitTypeId = 102L + index)
        }

        sut.recalculateBonuses(
            recalculateBonusesCommand = RecalculateBonusesCommand(referrerId = referrer.id)
        )
        val result = paymentRepository.findByReferrerIdAndLinkedEntityTypeIn(
            referrerId = referrer.id,
            linkedEntityTypes = setOf(LinkedEntityType.BONUS)
        )

        result shouldHaveSize 1
        result[0].linkedEntityType shouldBe LinkedEntityType.BONUS
    }
}