package com.holidu.referral.referee

import com.bookiply.referral.events.RefereeSignedUpEvent
import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.legalentity.Address
import com.holidu.referral.legalentity.Billing
import com.holidu.referral.legalentity.Individual
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC

class RefereeServiceTest @Autowired constructor(
    private val sut: RefereeService,
) : IntegrationTestBase() {
    @Test
    fun `should save referee on referee signUp event`() {
        val referrer = createReferrer(
            legalEntityId = 56L,
            referralCode = "Z65TYX96"
        )

        whenever(legalEntityServiceTokenClient.getLegalEntity(23L))
            .thenReturn(
                Individual(
                    id = 23L,
                    isAmbassador = true,
                    firstName = "<PERSON>",
                    lastName = "Doe",
                    address = Address("Street 1", "123456","München", "DE"),
                    billing = Billing( null,"DE1234567890")
                )
            )

        sut.onRefereeSignUp(
            refereeSignedUpEvent = RefereeSignedUpEvent(
                refereeLegalEntityId = 23L,
                referralCode = "Z65TYX96",
                createdAt = OffsetDateTime.now(UTC)
            )
        )
        val result = refereeRepository.findByLegalEntityId(23L)!!
        result.referrerId shouldBe referrer.id
        result.referrerLegalEntityId shouldBe 56L
    }

    @Test
    fun `should save referee name on referee signUp event`() {
        createReferrer(
            legalEntityId = 56L,
            referralCode = "Z65TYX96"
        )

        whenever(legalEntityServiceTokenClient.getLegalEntity(23L))
            .thenReturn(
                Individual(
                    id = 23L,
                    isAmbassador = true,
                    firstName = "John",
                    lastName = "Doe",
                    address = Address("Street 1", "123456","München", "DE"),
                    billing = Billing( null,"DE1234567890")
                )
            )

        sut.onRefereeSignUp(
            refereeSignedUpEvent = RefereeSignedUpEvent(
                refereeLegalEntityId = 23L,
                referralCode = "Z65TYX96",
                createdAt = OffsetDateTime.now(UTC)
            )
        )
        val result = refereeRepository.findByLegalEntityId(23L)!!
        result.name shouldBe "John D"
    }

    /*
    Referrer can change, the use case is:
    1. Host is referred to PMS, but uses the referral code of wrong Referrer
    2. Account manager corrects the referral code for the host, so correct Referrer receives the referral reward
     */
    @Test
    fun `should save referee on referee signUp event, if the referrer has changed`() {
        val referrerOne = createReferrer(
            legalEntityId = 5L,
            referralCode = "1212GH56"
        )
        val referrerTwo = createReferrer(
            legalEntityId = 44L,
            referralCode = "123KS987"
        )
        createReferee(
            legalEntityId = 23L,
            referrerId = referrerOne.id,
            referrerLegalEntityId = referrerOne.legalEntityId
        )

        sut.onRefereeSignUp(
            refereeSignedUpEvent = RefereeSignedUpEvent(
                refereeLegalEntityId = 23L,
                referralCode = referrerTwo.referralCode,
                createdAt = OffsetDateTime.now(UTC)
            )
        )
        val result = refereeRepository.findByLegalEntityId(23L)!!

        result.referrerId shouldBe referrerTwo.id
        result.referrerLegalEntityId shouldBe referrerTwo.legalEntityId
    }

    @Test
    fun `skip referee creation when already exists with the same referrer`() {
        val referrer = createReferrer(
            legalEntityId = 3L,
            referralCode = "4378GHDT"
        )
        val referee = createReferee(
            legalEntityId = 96L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
        )

        sut.onRefereeSignUp(refereeSignedUpEvent = RefereeSignedUpEvent(
            refereeLegalEntityId = 96L,
            referralCode = "4378GHDT",
            createdAt = OffsetDateTime.now(UTC)
        ))

        val existingReferee = refereeRepository.findByLegalEntityId(96L)!!
        // last update didn't change -> no update on 2nd same event happened
        existingReferee.updatedAt shouldBe referee.updatedAt
    }
}