package com.holidu.referral.payment

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.api.payment.bonus.BonusDto
import com.holidu.referral.api.payment.bonus.BonusesDto
import com.holidu.referral.api.payment.reward.RewardDto
import com.holidu.referral.api.payment.reward.RewardsDto
import com.holidu.referral.domain.property.ReferredProperty
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.springframework.test.web.reactive.server.expectBody
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC

class PaymentsControllerTest : IntegrationTestBase() {
    @Test
    fun `single reward with status PENDING is not reachable from the API by id`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = createReferredPublishedProperty(referrerId = referrer.id, refereeId = referee.id)
        val reward = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            unitTypeId = referredProperty.unitTypeId,
            type = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.PENDING
        )

        webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/rewards/{paymentId}", referrer.legalEntityId, reward.id)
            .exchange()
            .expectStatus().isNotFound
    }

    @Test
    fun `single reward is found by id`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = createReferredPublishedProperty(referrerId = referrer.id, refereeId = referee.id)
        val reward = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            unitTypeId = referredProperty.unitTypeId,
            type = LinkedEntityType.REFERRED_PROPERTY,
            creationDate = THREE_DAYS_AGO_AFTERNOON_UTC,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/rewards/{paymentId}", referrer.legalEntityId, reward.id)
            .exchange()
            .expectStatus().isOk
            .expectBody<RewardDto>()
            .returnResult()
            .responseBody!!

        result shouldBe RewardDto(
            id = reward.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            payoutAmount = reward.reward.toBigDecimal(),
            unitTypeId = reward.unitTypeId!!,
            createdAt = reward.creationDate
        )
    }

    @Test
    fun `single bonus with status PENDING is not reachable from the API by id`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = createReferredPublishedProperty(referrerId = referrer.id, refereeId = referee.id)
        val bonus = createPayment(
            referrerId = randomReferrerId(),
            referrerLegalEntityId = referrer.legalEntityId,
            type = LinkedEntityType.BONUS,
            bonusId = randomReferrerId(),
            state = PaymentState.PENDING
        )

        webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/bonuses/{paymentId}", referrer.legalEntityId, bonus.id)
            .exchange()
            .expectStatus().isNotFound
    }

    @Test
    fun `single bonus is found by id`() {
        val referrer = createReferrer()
        val bonus = createBonus(referrerId = referrer.id, numberOfPayments = 3)
        val bonusPayment = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            bonusId = bonus.id,
            creationDate = THREE_DAYS_AGO_AFTERNOON_UTC,
            type = LinkedEntityType.BONUS,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/bonuses/{paymentId}", referrer.legalEntityId, bonusPayment.id)
            .exchange()
            .expectStatus().isOk
            .expectBody<BonusDto>()
            .returnResult()
            .responseBody!!

        result shouldBe BonusDto(
            id = bonusPayment.id,
            referrerLegalEntityId = referrer.legalEntityId,
            payoutAmount = bonusPayment.reward.toBigDecimal(),
            createdAt = bonusPayment.creationDate,
            bonusType = 3
        )
    }

    @Test
    fun `rewards with status PENDING are not reachable from the API`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        createPayment(referrerId = referrer.id, state = PaymentState.PENDING, unitTypeId = 102L)
        createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            reward = 400,
            state = PaymentState.COMPLETED,
            unitTypeId = 123
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/rewards", referrer.legalEntityId)
            .exchange()
            .expectStatus().isOk
            .expectBody<RewardsDto>()
            .returnResult()
            .responseBody!!

        result.rewards shouldHaveSize 1
        result.rewards.first().referrerLegalEntityId shouldBe referrer.legalEntityId
        result.rewards.first().refereeLegalEntityId shouldBe referee.legalEntityId
        result.rewards.first().payoutAmount shouldBe BigDecimal.valueOf(400)
        result.rewards.first().unitTypeId shouldBe 123
        result.rewards.first().createdAt shouldBeUpToSeconds NOW
    }

    @Test
    fun `rewards are found for the referrer`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = createReferredPublishedProperty(referrerId = referrer.id, refereeId = referee.id)
        val firstReward = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            unitTypeId = referredProperty.unitTypeId,
            creationDate = THREE_DAYS_AGO_AFTERNOON_UTC,
            type = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/rewards", referrer.legalEntityId)
            .exchange()
            .expectStatus().isOk
            .expectBody<RewardsDto>()
            .returnResult()
            .responseBody!!

        result.rewards shouldBe listOf(
            RewardDto(
                id = firstReward.id,
                referrerLegalEntityId = referrer.legalEntityId,
                refereeLegalEntityId = referee.legalEntityId,
                payoutAmount = firstReward.reward.toBigDecimal(),
                unitTypeId = firstReward.unitTypeId!!,
                createdAt = firstReward.creationDate
            )
        )
    }

    @Test
    fun `bonuses with status PENDING are not reachable from the API`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val bonus = createBonus(referrerId = referrer.id, numberOfPayments = 3)
        createPayment(referrerId = referrer.id, state = PaymentState.PENDING, unitTypeId = 102L)
        createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            refereeLegalEntityId = referee.legalEntityId,
            bonusId = bonus.id,
            type = LinkedEntityType.BONUS,
            reward = 400,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/bonuses", referrer.legalEntityId)
            .exchange()
            .expectStatus().isOk
            .expectBody<BonusesDto>()
            .returnResult()
            .responseBody!!

        result.bonuses shouldHaveSize 1
        result.bonuses.first().referrerLegalEntityId shouldBe referrer.legalEntityId
        result.bonuses.first().payoutAmount shouldBe BigDecimal.valueOf(400)
        result.bonuses.first().createdAt shouldBeUpToSeconds NOW
    }

    @Test
    fun `bonuses are found for the referrer`() {
        val referrer = createReferrer()
        val firstBonus = createBonus(referrerId = referrer.id, numberOfPayments = 3)
        val secondBonus = createBonus(referrerId = referrer.id, numberOfPayments = 13)
        val firstBonusPayment = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            bonusId = firstBonus.id,
            creationDate = THREE_DAYS_AGO_AFTERNOON_UTC,
            type = LinkedEntityType.BONUS,
            state = PaymentState.COMPLETED
        )
        val secondBonusPayment = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            bonusId = secondBonus.id,
            creationDate = TWO_DAYS_AGO_AFTERNOON_UTC,
            type = LinkedEntityType.BONUS,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/bonuses", referrer.legalEntityId)
            .exchange()
            .expectStatus().isOk
            .expectBody<BonusesDto>()
            .returnResult()
            .responseBody!!

        result.bonuses shouldBe listOf(
            BonusDto(
                id = firstBonusPayment.id,
                referrerLegalEntityId = referrer.legalEntityId,
                payoutAmount = firstBonusPayment.reward.toBigDecimal(),
                createdAt = firstBonusPayment.creationDate,
                bonusType = 3
            ),
            BonusDto(
                id = secondBonusPayment.id,
                referrerLegalEntityId = referrer.legalEntityId,
                payoutAmount = secondBonusPayment.reward.toBigDecimal(),
                createdAt = secondBonusPayment.creationDate,
                bonusType = 3
            )
        )

    }

    @Test
    fun `reward's createdAt matches payment's creationDate`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = createReferredPublishedProperty(referrerId = referrer.id, refereeId = referee.id)
        val reward = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            unitTypeId = referredProperty.unitTypeId,
            creationDate = THREE_DAYS_AGO_AFTERNOON_UTC,
            type = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/rewards/{paymentId}", referrer.legalEntityId, reward.id)
            .exchange()
            .expectStatus().isOk
            .expectBody<RewardDto>()
            .returnResult()
            .responseBody!!

        result.createdAt shouldBe THREE_DAYS_AGO_AFTERNOON_UTC
    }

    @Test
    fun `bonus' createdAt matches payment's creationDate`() {
        val referrer = createReferrer()
        val bonus = createBonus(referrerId = referrer.id, numberOfPayments = 3)
        val bonusPayment = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            bonusId = bonus.id,
            creationDate = THREE_DAYS_AGO_AFTERNOON_UTC,
            type = LinkedEntityType.BONUS,
            state = PaymentState.COMPLETED
        )

        val result = webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/bonuses/{paymentId}", referrer.legalEntityId, bonusPayment.id)
            .exchange()
            .expectStatus().isOk
            .expectBody<BonusDto>()
            .returnResult()
            .responseBody!!

        result.createdAt shouldBe THREE_DAYS_AGO_AFTERNOON_UTC
    }

    @Test
    fun `404 Not found if the requested payment's actual type differs from the requested type`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = createReferredPublishedProperty(referrerId = referrer.id, refereeId = referee.id)
        val reward = createPayment(
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId,
            unitTypeId = referredProperty.unitTypeId,
            type = LinkedEntityType.REFERRED_PROPERTY,
            state = PaymentState.COMPLETED
        )

        webTestClient.get()
            .uri("/v1/referrers/{referrerLegalEntityId}/bonuses/{paymentId}", referrer.legalEntityId, reward.id)
            .exchange()
            .expectStatus().isNotFound
    }

    private fun createReferredPublishedProperty(referrerId: Long, refereeId: Long): ReferredProperty {
        return publishedProperty(
            referrerId = referrerId,
            refereeLegalEntityId = refereeId,
            publicationDate = OffsetDateTime.now(UTC)
        )
    }

    companion object {
        private val CURRENT_AFTERNOON_UTC = OffsetDateTime.now(UTC)
            .withHour(12)
            .withMinute(0)
            .withSecond(0)
            .withNano(0)

        private val THREE_DAYS_AGO_AFTERNOON_UTC: OffsetDateTime = CURRENT_AFTERNOON_UTC.minusDays(3)
        private val TWO_DAYS_AGO_AFTERNOON_UTC: OffsetDateTime = CURRENT_AFTERNOON_UTC.minusDays(2)
        private val ONE_DAY_AGO_AFTERNOON_UTC: OffsetDateTime = CURRENT_AFTERNOON_UTC.minusDays(1)
    }
}