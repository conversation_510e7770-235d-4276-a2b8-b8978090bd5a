package com.holidu.referral.payment

import com.bookiply.apartment.model.ApartmentTypeEnum
import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.bonus.Bonus
import com.holidu.referral.bonus.RecalculateBonusesCommand
import com.holidu.referral.payment.exception.PaymentCreationException
import com.holidu.referral.pms.client.FocusRegionClient
import feign.FeignException
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.mockito.kotlin.any
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import java.time.Instant
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.EnumSet

class PaymentServiceTest @Autowired constructor(
    private val sut: PaymentService,
    private val focusRegionClient: FocusRegionClient
) : IntegrationTestBase() {

    @BeforeEach
    fun setUp() {
        createRewardRuleForCurrentMonth()
    }

    @Test
    fun `payments are fetched correctly for a particular referrer`() {
        val referrer = createReferrer()
        val firstPayment = createPayment(
            referrerId = referrer.id,
            type = LinkedEntityType.REFERRED_PROPERTY,
            unitTypeId = 101L
        )
        val secondPayment = createPayment(
            referrerId = referrer.id,
            type = LinkedEntityType.REFERRED_PROPERTY,
            unitTypeId = 102L
        )

        val result = sut.getPayments(referrerId = referrer.id)

        result.map { it.referrerId }.toSet() shouldBe setOf(referrer.id)
        result.map { it.id } shouldContainExactlyInAnyOrder listOf(firstPayment.id, secondPayment.id)
    }


    @Test
    fun `throws PaymentCreationException error fetching focus region from PMS`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(referrerId = referrer.id, refereeLegalEntityId = referee.legalEntityId)

        whenever(focusRegionClient.getRegionByApartmentId(any())).thenThrow(FeignException::class.java)

        val exception = assertThrows<PaymentCreationException> {
            sut.createPaymentForReferredProperty(referredProperty)
        }

        exception.message shouldBe
                "Payment creation failed — error getting focus region for property ${referredProperty.id}"
    }


    @Test
    fun `payment is not created twice for the same property`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(referrerId = referrer.id, refereeLegalEntityId = referee.legalEntityId)
        val existingPayment = createPayment(
            referrerId = referrer.id,
            reward = 101,
            unitTypeId = referredProperty.unitTypeId,
        )

        sut.createPaymentForReferredProperty(referredProperty)

        val payment = paymentRepository.findByUnitTypeId(referredProperty.unitTypeId)!!
        payment shouldNotBe null
        payment.updatedAt shouldBe existingPayment.updatedAt

        verify(focusRegionClient, never()).getRegionByApartmentId(any())
    }

    @Test
    fun `payment is not created for properties published before april 2025`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            creationDate = LASTDAYOFMARCH2025.minusDays(1),
            publicationDate = LASTDAYOFMARCH2025
        )

        sut.createPaymentForReferredProperty(referredProperty)

        val payment = paymentRepository.findByUnitTypeId(referredProperty.unitTypeId)

        verify(focusRegionClient, never()).getRegionByApartmentId(any())

        payment shouldBe null
    }

    @Test
    fun `payment is not created for room if a payment for another room within the same parent listing exists`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val roomOne = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            propertyType = ApartmentTypeEnum.ROOM,
            parentListingId = 101L,
            unitTypeId = 102L,
        )
        val roomTwo = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            propertyType = ApartmentTypeEnum.ROOM,
            parentListingId = 101L,
            unitTypeId = 503L,
        )
        createPayment(
            referrerId = referrer.id,
            reward = 101,
            unitTypeId = roomOne.unitTypeId,
        )

        sut.createPaymentForReferredProperty(roomTwo)

        val payment = paymentRepository.findByUnitTypeId(roomTwo.unitTypeId)

        payment shouldBe null
        verify(focusRegionClient, never()).getRegionByApartmentId(any())
    }

    @Test
    fun `payment is created for eligible property`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
        )

        sut.createPaymentForReferredProperty(referredProperty)

        val payment = paymentRepository.findByUnitTypeId(referredProperty.unitTypeId)!!

        payment shouldNotBe null
        payment.creationDate.toEpochSecond() shouldBe referredProperty.publicationDate!!.toEpochSecond()
        payment.state shouldBe PaymentState.PENDING
    }

    @Test
    fun `payment is created with reward from the reward rules`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
        )

        sut.createPaymentForReferredProperty(referredProperty)

        val payment = paymentRepository.findByUnitTypeId(referredProperty.unitTypeId)!!

        payment shouldNotBe null
        payment.reward shouldBe 101
    }

    @Test
    fun `payment is created for the first published room in a multi unit property`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val roomOne = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            propertyType = ApartmentTypeEnum.ROOM,
            parentListingId = 101L,
            unitTypeId = 102L,
        )

        sut.createPaymentForReferredProperty(roomOne)

        val payment = paymentRepository.findByUnitTypeId(roomOne.unitTypeId)!!

        payment shouldNotBe null
        payment.creationDate.toEpochSecond() shouldBe roomOne.publicationDate!!.toEpochSecond()
    }

    @Test
    fun `payment is created for room if payments for other NON rooms within the same parent listing exists`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val bungalow = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            propertyType = ApartmentTypeEnum.BUNGALOW,
            parentListingId = 101L,
            unitTypeId = 101L,
        )
        val roomOne = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            propertyType = ApartmentTypeEnum.ROOM,
            parentListingId = 101L,
            unitTypeId = 102L,
        )
        createPayment(referrerId = referrer.id, unitTypeId = bungalow.unitTypeId)

        sut.createPaymentForReferredProperty(roomOne)

        val payment = paymentRepository.findByUnitTypeId(roomOne.unitTypeId)!!
        payment shouldNotBe null
        payment.creationDate shouldBeUpToSeconds roomOne.publicationDate
    }


    @Test
    fun `payments are fetched correctly for a particular referrer and type`() {
        val referrer = createReferrer()
        createPayment(referrerId = referrer.id, type = LinkedEntityType.REFERRED_PROPERTY,  unitTypeId = 101L)
        val bonus = createPayment(referrerId = referrer.id, type = LinkedEntityType.BONUS, bonusId = 1)

        val result = sut.getPayments(
            referrerId = referrer.id,
            types = EnumSet.of(LinkedEntityType.BONUS)
        )

        result shouldHaveSize 1
        result[0].id shouldBe bonus.id
    }

    @Test
    fun `payment for a referred published property leads to recalculation of bonuses for the referrer`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
        )

        sut.createPaymentForReferredProperty(publishedProperty = referredProperty)

        mockCommandPublisher.sentCommands shouldContainExactlyInAnyOrder listOf(RecalculateBonusesCommand(referrerId = referrer.id))
    }

    @Test
    fun `newly created referred property payment has a state of PENDING`() {
        val referrer = createReferrer()
        val referee = createReferee(referrerId = referrer.id, referrerLegalEntityId = referrer.legalEntityId)
        val referredProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
        )

        sut.createPaymentForReferredProperty(publishedProperty = referredProperty)
        val result = paymentRepository.findByUnitTypeId(referredProperty.unitTypeId)!!

        result shouldNotBe null
        result.state shouldBe PaymentState.PENDING
    }

    @Test
    fun `newly created bonus payment has a state of PENDING`() {
        val bonus = randomBonus()
        sut.createPaymentForBonus(bonus = bonus)

        val result = paymentRepository.findByBonusId(bonus.id)!!

        result shouldNotBe null
        result.state shouldBe PaymentState.PENDING
    }

    @Test
    fun `referrerLegalEntityId and refereeLegalEntityId are saved for the referred property payment `() {
        val referrer = createReferrer(legalEntityId = 156L)
        val referee = createReferee(
            legalEntityId = 4325L,
            referrerId = referrer.id,
            referrerLegalEntityId = referrer.legalEntityId
        )
        val referredProperty = publishedProperty(
            referrerId = referrer.id,
            refereeLegalEntityId = referee.legalEntityId,
            publicationDate = OffsetDateTime.now(UTC),
        )

        sut.createPaymentForReferredProperty(publishedProperty = referredProperty)
        val result = paymentRepository.findByUnitTypeId(referredProperty.unitTypeId)!!

        result.referrerLegalEntityId shouldBe referrer.legalEntityId
        result.refereeLegalEntityId shouldBe referee.legalEntityId
    }

    @Test
    fun `referrerLegalEntityId is saved for the bonus payment`() {
        val referrer = createReferrer(legalEntityId = 156L)
        val bonus = createBonus(referrerId = referrer.id, numberOfPayments = 3)

        sut.createPaymentForBonus(bonus = bonus)
        val result = paymentRepository.findByBonusId(bonus.id)!!

        result.referrerLegalEntityId shouldBe referrer.legalEntityId
    }

    @Test
    fun `deleted payments are ignored`() {
        val referrer = createReferrer(legalEntityId = 156L)
        createPayment(
            referrerId = referrer.id,
            deletedAt = Instant.now(),
            type = LinkedEntityType.REFERRED_PROPERTY,
            unitTypeId = 101L
        )

        val result = sut.getPayments(referrerId = referrer.id)

        result.size shouldBe 0
    }

    private fun createRewardRuleForCurrentMonth(reward: Int = 101) {
        createRewardRule(
            startDate = OffsetDateTime.now(UTC).minusMonths(1),
            endDate = OffsetDateTime.now(UTC).plusMonths(1),
            reward = reward
        )
    }

    private fun randomBonus(): Bonus = createBonus(referrerId = randomReferrerId(), numberOfPayments = 3)

    companion object {
        private val NOW = OffsetDateTime.now(UTC)
        private val LASTDAYOFMARCH2025 = OffsetDateTime.parse("2025-03-31T20:00:00+01:00")
    }
}