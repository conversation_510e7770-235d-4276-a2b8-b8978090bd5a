package com.holidu.referral.payment

import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.api.payment.PaymentCreatedEvent
import com.holidu.referral.api.payment.PaymentType
import com.holidu.referral.legalentity.Address
import com.holidu.referral.legalentity.Billing
import com.holidu.referral.legalentity.Individual
import com.holidu.referral.legalentity.Organization
import com.holidu.referral.referrer.Referrer
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired

class PaymentDispatchServiceTest @Autowired constructor(
    private val sut: PaymentDispatchService
) : IntegrationTestBase(){

    private lateinit var individualReferrerWithCompleteInfo: Referrer
    private lateinit var organisationReferrerWithCompleteInfo: Referrer
    private lateinit var individualReferrerWithIncompleteName: Referrer
    private lateinit var organisationReferrerWithIncompleteName: Referrer
    private lateinit var referrerWithIncompleteAddress: Referrer
    private lateinit var referrerWithIncompleteIban: Referrer

    @BeforeEach
    fun setUp() {
        individualReferrerWithCompleteInfo = createReferrer()
        organisationReferrerWithCompleteInfo = createReferrer()
        individualReferrerWithIncompleteName = createReferrer()
        organisationReferrerWithIncompleteName = createReferrer()
        referrerWithIncompleteAddress = createReferrer()
        referrerWithIncompleteIban = createReferrer()

        stubLegalEntityResponses()
    }

    @Test
    fun `pending payments for individuals with COMPLETE payment info should be marked complete and dispatched`(){
        val propertyPayment1 = createPayment(
            referrerId = individualReferrerWithCompleteInfo.id,
            referrerLegalEntityId = individualReferrerWithCompleteInfo.legalEntityId,
            state = PaymentState.PENDING,
        )
        val bonusPayment = createPayment(
            referrerId = individualReferrerWithCompleteInfo.id,
            referrerLegalEntityId = individualReferrerWithCompleteInfo.legalEntityId,
            state = PaymentState.PENDING,
            type = LinkedEntityType.BONUS
        )

        sut.validateAndDispatchPaymentEvents()

        paymentRepository.findOne(propertyPayment1.id)?.state shouldBe PaymentState.COMPLETED
        paymentRepository.findOne(bonusPayment.id)?.state shouldBe PaymentState.COMPLETED

        val events = mockEventPublisher.sentEvents
        events shouldContainExactlyInAnyOrder listOf(
            PaymentCreatedEvent(
                referrerLegalEntityId = individualReferrerWithCompleteInfo.legalEntityId,
                paymentId = propertyPayment1.id,
                type = PaymentType.REWARD
            ),
            PaymentCreatedEvent(
                referrerLegalEntityId = individualReferrerWithCompleteInfo.legalEntityId,
                paymentId = bonusPayment.id,
                type = PaymentType.BONUS
            )
        )
    }

    @Test
    fun `pending payments for organisations with COMPLETE payment info should be marked complete and dispatched`(){
        val propertyPayment1 = createPayment(
            referrerId = organisationReferrerWithCompleteInfo.id,
            referrerLegalEntityId = organisationReferrerWithCompleteInfo.legalEntityId,
            state = PaymentState.PENDING,
            unitTypeId = 12L
        )
        val bonusPayment = createPayment(
            referrerId = organisationReferrerWithCompleteInfo.id,
            referrerLegalEntityId = organisationReferrerWithCompleteInfo.legalEntityId,
            state = PaymentState.PENDING,
            type = LinkedEntityType.BONUS,
            bonusId = 1L
        )

        sut.validateAndDispatchPaymentEvents()

        paymentRepository.findOne(propertyPayment1.id)?.state shouldBe PaymentState.COMPLETED
        paymentRepository.findOne(bonusPayment.id)?.state shouldBe PaymentState.COMPLETED

        val events = mockEventPublisher.sentEvents
        events shouldContainExactlyInAnyOrder listOf(
            PaymentCreatedEvent(
                referrerLegalEntityId = organisationReferrerWithCompleteInfo.legalEntityId,
                paymentId = propertyPayment1.id,
                type = PaymentType.REWARD
            ),
            PaymentCreatedEvent(
                referrerLegalEntityId = organisationReferrerWithCompleteInfo.legalEntityId,
                paymentId = bonusPayment.id,
                type = PaymentType.BONUS
            )
        )
    }

    @Test
    fun `pending payments for referrers with INCOMPLETE address should remain pending and should not be dispatched`(){
        val propertyPayment1 = createPayment(
            referrerId = referrerWithIncompleteAddress.id,
            referrerLegalEntityId = referrerWithIncompleteAddress.legalEntityId,
            state = PaymentState.PENDING
        )
        sut.validateAndDispatchPaymentEvents()

        paymentRepository.findOne(propertyPayment1.id)?.state shouldBe PaymentState.PENDING

        mockEventPublisher.sentEvents shouldBe emptyList()
    }

    @Test
    fun `pending payments for referrers with INCOMPLETE Iban should remain pending and should not be dispatched`(){
        val propertyPayment1 = createPayment(
            referrerId = referrerWithIncompleteIban.id,
            referrerLegalEntityId = referrerWithIncompleteIban.legalEntityId,
            state = PaymentState.PENDING
        )
        sut.validateAndDispatchPaymentEvents()

        paymentRepository.findOne(propertyPayment1.id)?.state shouldBe PaymentState.PENDING

        mockEventPublisher.sentEvents shouldBe emptyList()
    }

    @Test
    fun `pending payments for individuals with INCOMPLETE name should remain pending and should not be dispatched`(){
        val propertyPayment1 = createPayment(
            referrerId = individualReferrerWithIncompleteName.id,
            referrerLegalEntityId = individualReferrerWithIncompleteName.legalEntityId,
            state = PaymentState.PENDING
        )
        sut.validateAndDispatchPaymentEvents()

        paymentRepository.findOne(propertyPayment1.id)?.state shouldBe PaymentState.PENDING

        mockEventPublisher.sentEvents shouldBe emptyList()
    }

    @Test
    fun `pending payments for organisations with INCOMPLETE name should remain pending and should not be dispatched`(){
        val propertyPayment1 = createPayment(
            referrerId = organisationReferrerWithIncompleteName.id,
            referrerLegalEntityId = organisationReferrerWithIncompleteName.legalEntityId,
            state = PaymentState.PENDING
        )
        sut.validateAndDispatchPaymentEvents()

        paymentRepository.findOne(propertyPayment1.id)?.state shouldBe PaymentState.PENDING

        mockEventPublisher.sentEvents shouldBe emptyList()
    }

    @Test
    fun `completed payments should not be dispatched again`(){
        createPayment(
            referrerId = individualReferrerWithCompleteInfo.id,
            referrerLegalEntityId = individualReferrerWithCompleteInfo.legalEntityId,
            state = PaymentState.COMPLETED
        )

        sut.validateAndDispatchPaymentEvents()

        mockEventPublisher.sentEvents shouldBe emptyList()
    }


    private fun createPayment(
        referrerId: Long,
        referrerLegalEntityId: Long,
        state: PaymentState,
        type: LinkedEntityType = LinkedEntityType.REFERRED_PROPERTY): Payment {

        return if (type == LinkedEntityType.REFERRED_PROPERTY) {
            val referredProperty = createdProperty(
                referrerId = referrerId,
                createReferee(
                    referrerId = referrerId,
                    referrerLegalEntityId = referrerLegalEntityId
                ).legalEntityId
            )

            createPayment(
                referrerId = referrerId,
                referrerLegalEntityId = referrerLegalEntityId,
                unitTypeId = referredProperty.unitTypeId,
                type = type,
                state = state,
            )
        } else {
            val bonus = createBonus(
                referrerId = referrerId,
                numberOfPayments = 3
            )

            createPayment(
                referrerId = referrerId,
                referrerLegalEntityId = referrerLegalEntityId,
                bonusId = bonus.id,
                state = state,
                type = type
            )
        }
    }

    private fun stubLegalEntityResponses() {
        whenever(legalEntityServiceTokenClient.getLegalEntity(individualReferrerWithCompleteInfo.legalEntityId)).thenReturn(
            Individual(
                id = individualReferrerWithCompleteInfo.legalEntityId,
                firstName = "Mark",
                lastName = "Shutter",
                address = Address(
                    street = "123 Main St",
                    city = "Berlin",
                    postCode = "10115",
                    country = "Germany"
                ),
                billing = Billing(
                    vat = "DE123456789",
                    iban = "**********************"
                ),
                isAmbassador = false
            )
        )

        whenever(legalEntityServiceTokenClient.getLegalEntity(organisationReferrerWithCompleteInfo.legalEntityId)).thenReturn(
            Organization(
                id = organisationReferrerWithCompleteInfo.legalEntityId,
                address = Address(
                    street = "123 Main St",
                    city = "Berlin",
                    postCode = "10115",
                    country = "Germany"
                ),
                billing = Billing(
                    vat = "DE123456789",
                    iban = "**********************"
                ),
                organisationName = "myOrg",
                isAmbassador = false,
                firstName = "Mark",
                lastName = "Shutter"
            )
        )

        whenever(legalEntityServiceTokenClient.getLegalEntity(referrerWithIncompleteAddress.legalEntityId)).thenReturn(
            Individual(
                id = referrerWithIncompleteAddress.legalEntityId,
                firstName = "John",
                lastName = "Doe",
                address = null,
                billing = Billing(
                    vat = "DE123456789",
                    iban = "**********************"
                ),
                isAmbassador = false
            )
        )


        whenever(legalEntityServiceTokenClient.getLegalEntity(referrerWithIncompleteIban.legalEntityId)).thenReturn(
            Individual(
                id = referrerWithIncompleteIban.legalEntityId,
                firstName = "John",
                lastName = "Doe",
                address = Address(
                    street = "123 Main St",
                    city = "Berlin",
                    postCode = "10115",
                    country = "Germany"
                ),
                billing = Billing(
                    vat = null,
                    iban = null
                ),
                isAmbassador = false
            )
        )

        whenever(legalEntityServiceTokenClient.getLegalEntity(individualReferrerWithIncompleteName.legalEntityId)).thenReturn(
            Individual(
                id = individualReferrerWithIncompleteName.legalEntityId,
                firstName = "John",
                lastName = null,
                address = Address(
                    street = "123 Main St",
                    city = "Berlin",
                    postCode = "10115",
                    country = "Germany"
                ),
                billing = Billing(
                    vat = null,
                    iban = null
                ),
                isAmbassador = false
            )
        )

        whenever(legalEntityServiceTokenClient.getLegalEntity(organisationReferrerWithIncompleteName.legalEntityId)).thenReturn(
            Organization(
                id = organisationReferrerWithIncompleteName.legalEntityId,
                organisationName = null,
                address = Address(
                    street = "123 Main St",
                    city = "Berlin",
                    postCode = "10115",
                    country = "Germany"
                ),
                billing = Billing(
                    vat = null,
                    iban = null
                ),
                isAmbassador = false,
                firstName = "John",
                lastName = "Doe"
            )
        )
    }
}