package com.holidu.referral

import com.fasterxml.jackson.databind.ObjectMapper
import com.holidu.firehose.utiltity.AvroSerializer
import com.holidu.referral.infrastructure.datalake.DatalakeRecord
import com.holidu.referral.infrastructure.datalake.FirehoseWriter
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import java.util.concurrent.CopyOnWriteArrayList

@TestConfiguration

class DatalakeTestConfig {
    @Bean
    fun mockFirehoseWriter(): MockFirehoseWriter = MockFirehoseWriter(objectMapper())
}

class MockFirehoseWriter(
    private val objectMapper: ObjectMapper
) : FirehoseWriter {
    val sentRecords: MutableList<String> = CopyOnWriteArrayList()
    private var throwExceptionOnCalls: Boolean = false

    override fun <R : DatalakeRecord> writeRecordToStream(
        firehoseRecord: R,
        firehoseStreamName: String
    ) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        sentRecords.add(firehoseRecord.writeValueAsStringWithAvro())
    }

    override fun <R : DatalakeRecord> writeRecordsToStream(
        firehoseRecords: Collection<R>,
        firehoseStreamName: String
    ) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        firehoseRecords.forEach {
            sentRecords.add(it.writeValueAsStringWithAvro())
        }
    }

    fun throwExceptionOnCalls() {
        throwExceptionOnCalls = true
    }

    fun resetExceptionFlag() {
        throwExceptionOnCalls = false
    }

    fun clearSentRecords() =
        sentRecords.clear()

    companion object {
        private fun throwException(): Nothing =
            throw RuntimeException("Failed to write record to firehose stream")

        private fun DatalakeRecord.writeValueAsStringWithAvro(): String =
            AvroSerializer.encode(data = this)
    }
}
