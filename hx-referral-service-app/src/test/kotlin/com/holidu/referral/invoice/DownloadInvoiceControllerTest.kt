package com.holidu.referral.invoice

import com.bookiply.rest.dto.SearchResponse
import com.holidu.finance.api.bookiply.ReferralInvoiceDto
import com.holidu.finance.api.invoice.InvoiceType
import com.holidu.referral.IntegrationTestBase
import com.holidu.referral.infrastructure.ErrorCode
import com.holidu.referral.infrastructure.external.legalentity.LegalEntityClientException
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.whenever
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.test.web.reactive.server.expectBody
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.random.Random

class DownloadInvoiceControllerTest : IntegrationTestBase() {
    @ParameterizedTest
    @ValueSource(ints = [-1, 13])
    fun `Bad request on incorrect month in request parameters`(
        month: Int
    ) {
        webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", month)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().isBadRequest
    }

    @ParameterizedTest
    @ValueSource(ints = [-100, 0, 2023])
    fun `Bad request on incorrect year in request parameters`(
        year: Int
    ) {
       webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", year)
                    .build()
            }
            .exchange()
            .expectStatus().isBadRequest
    }

    //    @Test
//    Disabling the test, since it is failing on Jenkins - will be fixed soon!
    fun `404 Not found if invoices are not found in FinApp`() {
        whenever(legalEntityUserTokenClient.findLegalEntityId())
            .thenReturn(REFERRER_LEGAL_ENTITY_ID)
        whenever(invoiceClient.searchLinkedInvoices(argThat { accountId == REFERRER_LEGAL_ENTITY_ID.toString() }))
            .thenReturn(SearchResponse.empty())

        webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().isNotFound
    }

    @Test
    fun `404 Not found if legal entity id is not found`() {
        // case: someone not yet registered in referral service
        // (but registered in Holidu world, so he has a valid JWT)
        // manually makes a request to download invoices endpoint
        whenever(legalEntityUserTokenClient.findLegalEntityId())
            .thenReturn(null)

        webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().isNotFound
    }

    @Test
    fun `502 Bad Gateway Try later if exception happens during request to Legal Entities service`() {
        whenever(legalEntityUserTokenClient.findLegalEntityId())
            .thenThrow(
                LegalEntityClientException(
                    message = "Failed to find legal entity.",
                    cause = RuntimeException()
                )
            )

        val result = webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().is5xxServerError
            .expectStatus().value { it == HttpStatus.BAD_GATEWAY.value() }
    }

    @Test
    fun `502 Bad Gateway if exception happened getting invoice ids`() {
        whenever(legalEntityUserTokenClient.findLegalEntityId())
            .thenReturn(REFERRER_LEGAL_ENTITY_ID)
        whenever(invoiceClient.searchLinkedInvoices(argThat { accountId == REFERRER_LEGAL_ENTITY_ID.toString() }))
            .thenThrow(
                RuntimeException("Failed to get invoice ids")
            )

        webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().is5xxServerError
            .expectStatus().value { it == HttpStatus.BAD_GATEWAY.value() }
    }

    @Test
    fun `502 Bad Gateway if exception during invoices download happens`() {
        whenever(legalEntityUserTokenClient.findLegalEntityId())
            .thenReturn(REFERRER_LEGAL_ENTITY_ID)
        whenever(invoiceClient.searchLinkedInvoices(argThat { accountId == REFERRER_LEGAL_ENTITY_ID.toString() }))
            .thenReturn(
                SearchResponse(
                    listOf(
                        referralInvoiceDto(id = 123L, accountId = REFERRER_LEGAL_ENTITY_ID),
                        referralInvoiceDto(id = 125L, accountId = REFERRER_LEGAL_ENTITY_ID)
                    ),
                    2
                )
            )

        whenever(invoiceClient.downloadInvoices(any()))
            .thenThrow(RuntimeException())

        webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().is5xxServerError
            .expectStatus().value { it == HttpStatus.BAD_GATEWAY.value() }
    }


    @Test
    fun `happy path - invoices are downloaded`() {
        val invoices = listOf(
            referralInvoiceDto(id = 432L, accountId = REFERRER_LEGAL_ENTITY_ID),
            referralInvoiceDto(id = 476L, accountId = REFERRER_LEGAL_ENTITY_ID)
        )
        whenever(legalEntityUserTokenClient.findLegalEntityId())
            .thenReturn(REFERRER_LEGAL_ENTITY_ID)
        whenever(invoiceClient.searchLinkedInvoices(argThat { accountId == REFERRER_LEGAL_ENTITY_ID.toString() }))
            .thenReturn(
                SearchResponse(
                    invoices,
                    2
                )
            )
        whenever(invoiceClient.downloadInvoices(any()))
            .thenReturn(
                ResponseEntity.ok()
                    .body(
                        ByteArrayResource(
                            sampleZip(filename = "/invoices/test-invoices.zip")
                        )
                    )
            )

        webTestClient.get()
            .uri { uriBuilder ->
                uriBuilder.path(INVOICE_DOWNLOAD_PATH)
                    .queryParam("month", APRIL)
                    .queryParam("year", YEAR_2025)
                    .build()
            }
            .exchange()
            .expectStatus().isOk
    }

    private fun sampleZip(filename: String): ByteArray =
        javaClass.getResourceAsStream(filename)!!.readBytes()

    companion object {
        private const val REFERRER_LEGAL_ENTITY_ID = 43267L
        private const val APRIL = 4
        private const val YEAR_2025 = 2025

        private fun referralInvoiceDto(
            id: Long = Random.nextLong(from = 1, until = Long.MAX_VALUE),
            accountId: Long
        ): ReferralInvoiceDto {
            val apartmentId = Random.nextLong(from = 1, until = Long.MAX_VALUE)

            return ReferralInvoiceDto(
                id,
                id.toString(),
                LocalDateTime.now().minusDays(1),
                InvoiceType.REFERRAL,
                accountId.toString(),
                LocalDate.now().minusDays(1),
                apartmentId
            )
    }


    }
}
