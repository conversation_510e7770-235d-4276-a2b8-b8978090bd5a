package com.holidu.referral

import com.bookiply.infrastructure.events.AbstractCommand
import com.bookiply.infrastructure.events.AbstractEvent
import com.bookiply.infrastructure.events.publishers.CommandParams
import com.bookiply.infrastructure.events.publishers.CommandPublisher
import com.bookiply.infrastructure.events.publishers.EventParams
import com.bookiply.infrastructure.events.publishers.EventPublisher
import com.bookiply.infrastructure.events.publishers.MockMessagePublisher
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean

@TestConfiguration
class MockPublishersTestConfig {
    @Bean
    fun eventPublisher(): FailableMockEventPublisher = FailableMockEventPublisher()

    @Bean
    fun commandPublisher(): FailableMockCommandPublisher = FailableMockCommandPublisher()
}

class FailableMockEventPublisher : MockMessagePublisher<AbstractEvent>(), EventPublisher {
    val sentEvents: List<AbstractEvent>
        get() = super.sentMessages
    private var throwExceptionOnCalls = false

    override fun publishEvent(event: AbstractEvent, params: EventParams) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        sendMessage(event, params, null)
    }

    override fun republishForMe(event: AbstractEvent, params: EventParams, subscriberClass: Class<*>) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        sendMessage(event, params, subscriberClass)
    }

    override fun publishEvent(event: AbstractEvent) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        sendMessage(event, null, null)
    }

    override fun publishEvents(events: List<AbstractEvent>) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        events.forEach { this.publishEvent(it) }
    }

    override fun publishEvents(events: List<AbstractEvent>, params: EventParams) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        events.forEach { publishEvent(it, params) }
    }

    override fun republishEvents(events: List<AbstractEvent>, params: EventParams, subscriberClass: Class<*>) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        events.forEach { republishForMe(it, params, subscriberClass) }
    }

    fun throwExceptionOnCalls() {
        throwExceptionOnCalls = true
    }

    fun resetExceptionFlag() {
        throwExceptionOnCalls = false
    }

    fun cleanSentEvents() = super.cleanSentMessages()

    companion object {
        private fun throwException(): Nothing =
            throw RuntimeException("Exception during sending event")
    }
}

class FailableMockCommandPublisher : MockMessagePublisher<AbstractCommand>(), CommandPublisher {
    val sentCommands: List<AbstractCommand>
        get() = sentMessages
    private var throwExceptionOnCalls = false

    override fun sendMessage(command: AbstractCommand) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        super.sendMessage(message = command, params = null, republishFor = null)
    }

    override fun sendMessage(
        command: AbstractCommand,
        commandParams: CommandParams?
    ) {
        if (throwExceptionOnCalls) {
            throwException()
        }

        super.sendMessage(message = command, params = commandParams, republishFor = null)
    }

    fun throwExceptionOnCalls() {
        throwExceptionOnCalls = true
    }

    fun resetExceptionFlag() {
        throwExceptionOnCalls = false
    }

    fun cleanSentCommands() = super.cleanSentMessages()

    companion object {
        private fun throwException(): Nothing =
            throw RuntimeException("Exception during sending command")
    }
}