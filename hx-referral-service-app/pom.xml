<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.holidu</groupId>
        <artifactId>hx-referral-service</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>hx-referral-service-app</artifactId>
    <version>0.0.1-SNAPSHOT</version>

    <properties>
        <referral-service-api.version>0.5.0</referral-service-api.version>
        <java.version>21</java.version>
        <kotlin.version>1.9.25</kotlin.version>
        <bookiply-spring-utils.version>2.0.5</bookiply-spring-utils.version>
        <logstash-logback-encoder.version>8.0</logstash-logback-encoder.version>
        <spring-cloud.version>3.3.0</spring-cloud.version>
        <spring-cloud.version>2024.0.0</spring-cloud.version>
        <spring-cloud-aws.version>3.3.0</spring-cloud-aws.version>
        <bookiply-api.version>2.66.0</bookiply-api.version>
        <finance-api.version>5.1.0</finance-api.version>
        <elasticsearch-apm.version>1.53.0</elasticsearch-apm.version>
        <bookiply-utils.version>2.0.3</bookiply-utils.version>
        <bookiply-events.version>3.0.11</bookiply-events.version>
        <bookiply-events-db.version>3.0.5</bookiply-events-db.version>
        <bookiply-locking.version>3.0.4</bookiply-locking.version>
        <bookiply-logging.version>3.0.0</bookiply-logging.version>
        <jackson-datatype-jsr310.version>2.18.3</jackson-datatype-jsr310.version>
        <mockito.kotlin.version>5.4.0</mockito.kotlin.version>
        <kotest.version>5.9.1</kotest.version>
        <sentry.version>7.10.0</sentry.version>
        <archunit.version>1.4.1</archunit.version>
        <apache.avro.version>1.12.0</apache.avro.version>
        <firehoseclientsdk.version>1.0.92</firehoseclientsdk.version>
        <aws.firehose.version>2.31.50</aws.firehose.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.awspring.cloud</groupId>
                <artifactId>spring-cloud-aws-dependencies</artifactId>
                <version>${spring-cloud-aws.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.holidu</groupId>
            <artifactId>hx-referral-service-api</artifactId>
            <version>${referral-service-api.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-graphql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <!-- For Finance API -->
            <artifactId>feign-okhttp</artifactId>
        </dependency>


        <dependency>
            <groupId>io.micrometer</groupId>
            <artifactId>micrometer-registry-prometheus</artifactId>
        </dependency>
        <dependency>
            <groupId>io.github.openfeign</groupId>
            <artifactId>feign-micrometer</artifactId>
        </dependency>

        <dependency>
            <groupId>jakarta.jms</groupId>
            <artifactId>jakarta.jms-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jms</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.activemq</groupId>
            <artifactId>activemq-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-kotlin</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <!-- To support OffsetDateTime in JSON mapping -->
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson-datatype-jsr310.version}</version>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-reflect</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-stdlib</artifactId>
        </dependency>

        <dependency>
            <groupId>io.awspring.cloud</groupId>
            <artifactId>spring-cloud-aws-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>io.awspring.cloud</groupId>
            <artifactId>spring-cloud-aws-starter-secrets-manager</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        </dependency>

        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>sts</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.postgresql</groupId>
            <artifactId>postgresql</artifactId>
        </dependency>
        <dependency>
            <groupId>org.liquibase</groupId>
            <artifactId>liquibase-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-spring-utils</artifactId>
            <version>${bookiply-spring-utils.version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-logback-encoder.version}</version>
        </dependency>

        <dependency>
            <groupId>co.elastic.apm</groupId>
            <artifactId>apm-agent-attach</artifactId>
            <version>${elasticsearch-apm.version}</version>
        </dependency>

        <dependency>
            <groupId>co.elastic.apm</groupId>
            <artifactId>apm-agent-api</artifactId>
            <version>${elasticsearch-apm.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-events</artifactId>
            <version>${bookiply-events.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-events-db</artifactId>
            <version>${bookiply-events-db.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-api</artifactId>
            <version>${bookiply-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.holidu.finance</groupId>
            <artifactId>finance-api</artifactId>
            <version>${finance-api.version}</version>
        </dependency>
        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-utils</artifactId>
            <version>${bookiply-utils.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-locking</artifactId>
            <version>${bookiply-locking.version}</version>
        </dependency>

        <dependency>
            <groupId>com.bookiply</groupId>
            <artifactId>bookiply-logging</artifactId>
            <version>${bookiply-logging.version}</version>
        </dependency>

        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-spring-boot-starter-jakarta</artifactId>
            <version>${sentry.version}</version>
        </dependency>
        <dependency>
            <groupId>io.sentry</groupId>
            <artifactId>sentry-logback</artifactId>
            <version>${sentry.version}</version>
        </dependency>

        <!-- Datalake indexing -->
        <dependency>
            <groupId>org.apache.avro</groupId>
            <artifactId>avro</artifactId>
            <version>${apache.avro.version}</version>
        </dependency>
        <dependency>
            <groupId>com.holidu</groupId>
            <artifactId>firehoseclientsdk</artifactId>
            <version>${firehoseclientsdk.version}</version>
            <classifier>jdk21</classifier>
        </dependency>
        <dependency>
            <groupId>software.amazon.awssdk</groupId>
            <artifactId>firehose</artifactId>
            <version>${aws.firehose.version}</version>
        </dependency>
        <!-- Used by com.holidu.firehose.FirehoseDispatcher -->
        <dependency>
            <groupId>org.jetbrains.kotlinx</groupId>
            <artifactId>kotlinx-coroutines-core</artifactId>
        </dependency>

        <!-- TEST DEPENDENCIES -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito.kotlin</groupId>
            <artifactId>mockito-kotlin</artifactId>
            <version>${mockito.kotlin.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains.kotlin</groupId>
            <artifactId>kotlin-test-junit5</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.kotest</groupId>
            <artifactId>kotest-assertions-core-jvm</artifactId>
            <version>${kotest.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webflux</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.graphql</groupId>
            <artifactId>spring-graphql-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>postgresql</artifactId>
            <version>${testcontainers.version}</version>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>com.tngtech.archunit</groupId>
            <artifactId>archunit-junit5</artifactId>
            <version>${archunit.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <repositories>
        <repository>
            <id>holidu-internal-releases-packagecloud</id>
            <url>
                https://packagecloud.io/priv/************************************************/holidu/internal-releases/maven2
            </url>
        </repository>
        <repository>
            <id>holidu-internal-snapshots-packagecloud</id>
            <url>
                https://packagecloud.io/priv/************************************************/holidu/internal-snapshots/maven2
            </url>
        </repository>
    </repositories>

    <build>
        <sourceDirectory>${project.basedir}/src/main/kotlin</sourceDirectory>
        <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>

        <plugins>
            <!-- This plugin is used to generate Java classes from Avro schema (.avsc) files -->
            <plugin>
                <groupId>org.apache.avro</groupId>
                <artifactId>avro-maven-plugin</artifactId>
                <version>${apache.avro.version}</version>
                <executions>
                    <execution>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>schema</goal>
                        </goals>
                        <configuration>
                            <sourceDirectory>${project.basedir}/src/main/avro</sourceDirectory>
                            <outputDirectory>${project.build.directory}/generated-sources/avro</outputDirectory>
                            <enableDecimalLogicalType>true</enableDecimalLogicalType>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
